# Makefile for libpng
# 16-bit Borland C++ (Note: All modules are compiled in C mode)
# To build the library, do:
#       "make -fmakefile.bor -DMODEL=c"
# or:   "make -fmakefile.bor -DMODEL=l"
#
# ------------ Borland C++ ------------

### Absolutely necessary for this makefile to work
.AUTODEPEND

## Where zlib.h, zconf.h and zlib_MODEL.lib are
ZLIB_DIR=..\zlib

## Compiler, linker and lib stuff
CC=bcc
LD=bcc
LIB=tlib

!ifndef MODEL
MODEL=l
!endif

MODEL_ARG=-m$(MODEL)

#TARGET_CPU=3
# 2 = 286, 3 = 386, etc.
!ifndef TARGET_CPU
TARGET_CPU=2
!endif

# Use this if you don't want Borland's fancy exception handling
# (for Borland C++ 4.0 or later)
#NOEHLIB=noeh$(MODEL).lib

!ifdef DEBUG
CDEBUG=-v
LDEBUG=-v
!else
CDEBUG=
LDEBUG=
!endif

# STACKOFLOW=1
!ifdef STACKOFLOW
CDEBUG=$(CDEBUG) -N
LDEBUG=$(LDEBUG) -N
!endif

# -X- turn on dependency generation in the object file
# -w  set all warnings on
# -O2 optimize for speed
# -Z  global optimization
CFLAGS=-O2 -Z -X- -w -I$(ZLIB_DIR) -$(TARGET_CPU) $(MODEL_ARG) $(CDEBUG)

# -M  generate map file
LDFLAGS=-M -L$(ZLIB_DIR) $(MODEL_ARG) $(LDEBUG)

## Variables

OBJS = \
	png.obj \
	pngerror.obj \
	pngget.obj \
	pngmem.obj \
	pngpread.obj \
	pngread.obj \
	pngrio.obj \
	pngrtran.obj \
	pngrutil.obj \
	pngset.obj \
	pngtrans.obj \
	pngwio.obj \
	pngwrite.obj \
	pngwtran.obj \
	pngwutil.obj

LIBOBJS = \
	+png.obj \
	+pngerror.obj \
	+pngget.obj \
	+pngmem.obj \
	+pngpread.obj \
	+pngread.obj \
	+pngrio.obj \
	+pngrtran.obj \
	+pngrutil.obj \
	+pngset.obj \
	+pngtrans.obj \
	+pngwio.obj \
	+pngwrite.obj \
	+pngwtran.obj \
	+pngwutil.obj

LIBNAME=libpng$(MODEL).lib

## Implicit rules

# Braces let make "batch" calls to the compiler,
# 2 calls instead of 12; space is important.
.c.obj:
	$(CC) $(CFLAGS) -c {$*.c }

.c.exe:
	$(CC) $(CFLAGS) $(LDFLAGS) $*.c $(LIBNAME) zlib_$(MODEL).lib $(NOEHLIB)

## Major targets

all: libpng pngtest

# try !inlude scripts\pnglibconf.mak for more options
pnglibconf.h: scripts\pnglibconf.h.prebuilt
	copy scripts\pnglibconf.h.prebuilt $@

libpng: $(LIBNAME)

pngtest: pngtest$(MODEL).exe

test: pngtest$(MODEL).exe
	pngtest$(MODEL)

## Minor Targets

png.obj: png.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngerror.obj: pngerror.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngget.obj: pngget.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngmem.obj: pngmem.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngpread.obj: pngpread.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngread.obj: pngread.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrio.obj: pngrio.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrtran.obj: pngrtran.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrutil.obj: pngrutil.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngset.obj: pngset.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngtrans.obj: pngtrans.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwio.obj: pngwio.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwrite.obj: pngwrite.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwtran.obj: pngwtran.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwutil.obj: pngwutil.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h

$(LIBNAME): $(OBJS)
	-del $(LIBNAME)
	$(LIB) $(LIBNAME) @&&|
$(LIBOBJS), libpng$(MODEL)
|

pngtest$(MODEL).obj: pngtest.c png.h pngconf.h pnglibconf.h
	$(CC) $(CFLAGS) -opngtest$(MODEL) -c pngtest.c

pngtest$(MODEL).exe: pngtest$(MODEL).obj
	$(LD) $(LDFLAGS) pngtest$(MODEL).obj $(LIBNAME) zlib_$(MODEL).lib $(NOEHLIB)

# Clean up anything else you want
clean:
	-del pnglibconf.h
	-del *.obj
	-del *.exe
	-del *.lib
	-del *.lst
	-del *.map

# End of makefile for libpng
