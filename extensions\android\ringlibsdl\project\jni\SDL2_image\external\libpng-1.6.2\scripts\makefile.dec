# makefile for libpng on DEC Alpha Unix
# Copyright (C) 2000-2002, 2006, 2010-2011 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Library name:
PNGMAJ = 16
LIBNAME = libpng16

# Shared library names:
LIBSO=$(LIBNAME).so
LIBSOMAJ=$(LIBNAME).so.$(PNGMAJ)
LIBSOREL=$(LIBSOMAJ).$(RELEASE)
OLDSO=libpng.so

# Utilities:
AR_RC=ar rc
CC=cc
MKDIR_P=mkdir
LN_SF=ln -f -s
RANLIB=ranlib
RM_F=/bin/rm -f

# where make install puts libpng.a and png.h
prefix=/usr/local
exec_prefix=$(prefix)
INCPATH=$(prefix)/include
LIBPATH=$(exec_prefix)/lib
MANPATH=$(prefix)/man
BINPATH=$(exec_prefix)/bin

# override DESTDIR= on the make install command line to easily support
# installing into a temporary location.  Example:
#
#    make install DESTDIR=/tmp/build/libpng
#
# If you're going to install into a temporary location
# via DESTDIR, $(DESTDIR)$(prefix) must already exist before
# you execute make install.
DESTDIR=

DB=$(DESTDIR)$(BINPATH)
DI=$(DESTDIR)$(INCPATH)
DL=$(DESTDIR)$(LIBPATH)
DM=$(DESTDIR)$(MANPATH)

# Where the zlib library and include files are located
#ZLIBLIB=/usr/local/lib
#ZLIBINC=/usr/local/include
ZLIBLIB=../zlib
ZLIBINC=../zlib

CFLAGS=-std -w1 -I$(ZLIBINC) -O # -g -DPNG_DEBUG=1
LDFLAGS=-L$(ZLIBLIB) -rpath $(ZLIBLIB) libpng.a -lz -lm

OBJS = png.o pngset.o pngget.o pngrutil.o pngtrans.o pngwutil.o \
	pngread.o pngrio.o pngwio.o pngwrite.o pngrtran.o \
	pngwtran.o pngmem.o pngerror.o pngpread.o

all: $(LIBSO) libpng.a pngtest libpng.pc libpng-config

# see scripts/pnglibconf.mak for more options
pnglibconf.h: scripts/pnglibconf.h.prebuilt
	cp scripts/pnglibconf.h.prebuilt $@

libpng.a: $(OBJS)
	$(AR_RC) $@  $(OBJS)
	$(RANLIB) $@

libpng.pc:
	cat scripts/libpng.pc.in | sed -e s!@prefix@!$(prefix)! \
	-e s!@exec_prefix@!$(exec_prefix)! \
	-e s!@libdir@!$(LIBPATH)! \
	-e s!@includedir@!$(INCPATH)! \
	-e s!-lpng16!-lpng16\ -lz\ -lm! > libpng.pc

libpng-config:
	( cat scripts/libpng-config-head.in; \
	echo prefix=\"$(prefix)\"; \
	echo I_opts=\"-I$(INCPATH)/$(LIBNAME)\"; \
	echo ccopts=\"-std\"; \
	echo L_opts=\"-L$(LIBPATH)\"; \
	echo libs=\"-lpng16 -lz -lm\"; \
	cat scripts/libpng-config-body.in ) > libpng-config
	chmod +x libpng-config

$(LIBSO): $(LIBSOMAJ)
	$(LN_SF) $(LIBSOMAJ) $(LIBSO)

$(LIBSOMAJ): $(OBJS)
	$(CC) -shared -o $@ $(OBJS) -L$(ZLIBLIB) \
	-soname $(LIBSOMAJ)

pngtest: pngtest.o libpng.a
	$(CC) -o pngtest $(CFLAGS) pngtest.o $(LDFLAGS)

test: pngtest
	./pngtest

install-headers: png.h pngconf.h pnglibconf.h
	-@if [ ! -d $(DI) ]; then $(MKDIR_P) $(DI); fi
	-@if [ ! -d $(DI)/$(LIBNAME) ]; then $(MKDIR_P) $(DI)/$(LIBNAME); fi
	cp png.h pngconf.h pnglibconf.h $(DI)/$(LIBNAME)
	chmod 644 $(DI)/$(LIBNAME)/png.h $(DI)/$(LIBNAME)/pngconf.h $(DI)/$(LIBNAME)/pnglibconf.h
	-@/bin/rm -f $(DI)/png.h $(DI)/pngconf.h $(DI)/pnglibconf.h
	-@/bin/rm -f $(DI)/libpng
	(cd $(DI); $(LN_SF)(LIBNAME) libpng; $(LN_SF)(LIBNAME)/* .)

install-static: install-headers libpng.a
	-@if [ ! -d $(DL) ]; then $(MKDIR_P) $(DL); fi
	cp libpng.a $(DL)/$(LIBNAME).a
	chmod 644 $(DL)/$(LIBNAME).a
	-@/bin/rm -f $(DL)/libpng.a
	(cd $(DL); $(LN_SF)(LIBNAME).a libpng.a)

install-shared: install-headers $(LIBSOMAJ) libpng.pc
	-@if [ ! -d $(DL) ]; then $(MKDIR_P) $(DL); fi
	-@$(RM_F) $(DL)/$(LIBSO)
	-@$(RM_F) $(DL)/$(LIBSOREL)
	-@$(RM_F) $(DL)/$(OLDSO)
	cp $(LIBSOMAJ) $(DL)/$(LIBSOREL)
	chmod 755 $(DL)/$(LIBSOREL)
	(cd $(DL); \
	$(LN_SF) $(LIBSOREL) $(LIBSO); \
	$(LN_SF) $(LIBSO) $(OLDSO))
	-@if [ ! -d $(DL)/pkgconfig ]; then $(MKDIR_P) $(DL)/pkgconfig; fi
	-@$(RM_F) $(DL)/pkgconfig/$(LIBNAME).pc
	-@$(RM_F) $(DL)/pkgconfig/libpng.pc
	cp libpng.pc $(DL)/pkgconfig/$(LIBNAME).pc
	chmod 644 $(DL)/pkgconfig/$(LIBNAME).pc
	(cd $(DL)/pkgconfig; $(LN_SF) $(LIBNAME).pc libpng.pc)

install-man: libpng.3 libpngpf.3 png.5
	-@if [ ! -d $(DM) ]; then $(MKDIR_P) $(DM); fi
	-@if [ ! -d $(DM)/man3 ]; then $(MKDIR_P) $(DM)/man3; fi
	-@/bin/rm -f $(DM)/man3/libpng.3
	-@/bin/rm -f $(DM)/man3/libpngpf.3
	cp libpng.3 $(DM)/man3
	cp libpngpf.3 $(DM)/man3
	-@if [ ! -d $(DM)/man5 ]; then $(MKDIR_P) $(DM)/man5; fi
	-@/bin/rm -f $(DM)/man5/png.5
	cp png.5 $(DM)/man5

install-config: libpng-config
	-@if [ ! -d $(DB) ]; then $(MKDIR_P) $(DB); fi
	-@/bin/rm -f $(DB)/libpng-config
	-@/bin/rm -f $(DB)/$(LIBNAME)-config
	cp libpng-config $(DB)/$(LIBNAME)-config
	chmod 755 $(DB)/$(LIBNAME)-config
	(cd $(DB); $(LN_SF)(LIBNAME)-config libpng-config)

install: install-static install-shared install-man install-config

# If you installed in $(DESTDIR), test-installed won't work until you
# move the library to its final location.  Use test-dd to test it
# before then.

test-dd:
	echo
	echo Testing installed dynamic shared library in $(DL).
	$(CC) -w1 -I$(DI) -I$(ZLIBINC) \
	   `$(BINPATH)/$(LIBNAME)-config --cflags` pngtest.c \
	   -L$(DL) -L$(ZLIBLIB)  -R$(ZLIBLIB) -R$(DL) \
	   -o pngtestd `$(BINPATH)/$(LIBNAME)-config --ldflags`
	./pngtestd pngtest.png

test-installed:
	echo
	echo Testing installed dynamic shared library.
	$(CC) -w1 -I$(ZLIBINC) \
	   `$(BINPATH)/$(LIBNAME)-config --cflags` pngtest.c \
	   -L$(ZLIBLIB) -R$(ZLIBLIB) \
	   -o pngtesti `$(BINPATH)/$(LIBNAME)-config --ldflags`
	./pngtesti pngtest.png

clean:
	/bin/rm -f *.o libpng.a pngtest pngtesti pngout.png \
	libpng-config $(LIBSO) $(LIBSOMAJ)* \
	libpng.pc pnglibconf.h

# DO NOT DELETE THIS LINE -- make depend depends on it.

png.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngerror.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrio.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwio.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngmem.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngset.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngget.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngtrans.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwrite.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwtran.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwutil.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngpread.o: png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h

pngtest.o: png.h pngconf.h pnglibconf.h
