# Commodore Amiga Makefile
# makefile for libpng and SAS C V6.5x compiler
# Copyright (C) 1995-2000 <PERSON> Faust
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h
#
# Location/path of zlib include files
ZLIB=/zlib
#compiler
CC=sc
#compiler flags
# WARNING: a bug in V6.51 causes bad code with OPTGO
#          So use V6.55 or set NOOPTGO!!!!!!!!!
CFLAGS= NOSTKCHK PARMS=REG OPTIMIZE OPTGO OPTPEEP OPTINLOCAL OPTINL\
	OPTLOOP OPTRDEP=4 OPTDEP=4 OPTCOMP=4 INCLUDEDIR=$(ZLIB) \
	DEFINE=PNG_INTERNAL
#linker flags
LDFLAGS= SD ND BATCH
#link libs
LDLIBS= libpng.lib libgz.lib LIB:scm.lib LIB:sc.lib Lib:amiga.lib
# linker
LN= slink
# file deletion command
RM= delete quiet
# file copy command?
CP= copy
# library (.lib) file creation command
AR= oml
# make directory command
MKDIR= makedir

OBJS = png.o pngset.o pngget.o pngrutil.o pngtrans.o pngwutil.o \
	pngread.o pngrio.o pngwio.o pngwrite.o pngrtran.o \
	pngwtran.o pngmem.o pngerror.o pngpread.o

all: libpng.lib pngtest

libpng.lib: $(OBJS)
-$(RM) libpng.lib
$(AR) libpng.lib r $(OBJS)

$(OBJS): pngpriv.h png.h pngconf.h pnglibconf.h pnginfo.h pngstruct.h pngdebug.h

pnglibconf.h: scripts/pnglibconf.h.prebuilt
$(CP) scripts/pnglibconf.h.prebuilt pnglibconf.h

pngtest: pngtest.o libpng.lib
$(LN) <WITH <
$(LDFLAGS)
TO pngtest
FROM LIB:c.o pngtest.o
LIB $(LDLIBS)
<

