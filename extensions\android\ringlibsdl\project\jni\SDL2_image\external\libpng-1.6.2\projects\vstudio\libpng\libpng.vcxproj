<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug Library|Win32">
      <Configuration>Debug Library</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release Library|Win32">
      <Configuration>Release Library</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{D6973076-9317-4EF2-A0B8-B7A18AC0713E}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>libpng</RootNamespace>
  </PropertyGroup>
  <Import Project="$(SolutionDir)\zlib.props" />
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug Library|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release Library|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug Library|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release Library|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <CustomBuildBeforeTargets>
    </CustomBuildBeforeTargets>
    <TargetName>$(ProjectName)16</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug Library|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <CustomBuildBeforeTargets />
    <TargetName>$(ProjectName)16</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <CustomBuildBeforeTargets>
    </CustomBuildBeforeTargets>
    <TargetName>$(ProjectName)16</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release Library|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <CustomBuildBeforeTargets />
    <TargetName>$(ProjectName)16</TargetName>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <MinimalRebuild>false</MinimalRebuild>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <TreatWChar_tAsBuiltInType>false</TreatWChar_tAsBuiltInType>
      <PrecompiledHeaderFile>pngpriv.h</PrecompiledHeaderFile>
      <BrowseInformation>true</BrowseInformation>
      <CompileAs>CompileAsC</CompileAs>
      <StringPooling>true</StringPooling>
      <DisableSpecificWarnings>4996;4127</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>$(ZLibSrcDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <TreatWarningAsError>true</TreatWarningAsError>
      <Optimization>Disabled</Optimization>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalDependencies>zlib.lib</AdditionalDependencies>
      <Version>16</Version>
      <AdditionalLibraryDirectories>$(OutDir)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug Library|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <MinimalRebuild>false</MinimalRebuild>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <Optimization>Disabled</Optimization>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <TreatWChar_tAsBuiltInType>false</TreatWChar_tAsBuiltInType>
      <PrecompiledHeaderFile>pngpriv.h</PrecompiledHeaderFile>
      <BrowseInformation>true</BrowseInformation>
      <CompileAs>CompileAsC</CompileAs>
      <StringPooling>true</StringPooling>
      <DisableSpecificWarnings>4996;4127</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>$(ZLibSrcDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <TreatWarningAsError>true</TreatWarningAsError>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;_USRDLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <TreatWChar_tAsBuiltInType>false</TreatWChar_tAsBuiltInType>
      <PrecompiledHeaderFile>pngpriv.h</PrecompiledHeaderFile>
      <BrowseInformation>true</BrowseInformation>
      <CompileAs>CompileAsC</CompileAs>
      <StringPooling>true</StringPooling>
      <MinimalRebuild>false</MinimalRebuild>
      <DisableSpecificWarnings>4996;4127</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>$(ZLibSrcDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <TreatWarningAsError>true</TreatWarningAsError>
      <Optimization>Full</Optimization>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalDependencies>zlib.lib</AdditionalDependencies>
      <Version>16</Version>
      <AdditionalLibraryDirectories>$(OutDir)</AdditionalLibraryDirectories>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release Library|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <FloatingPointExceptions>false</FloatingPointExceptions>
      <TreatWChar_tAsBuiltInType>false</TreatWChar_tAsBuiltInType>
      <PrecompiledHeaderFile>pngpriv.h</PrecompiledHeaderFile>
      <BrowseInformation>true</BrowseInformation>
      <CompileAs>CompileAsC</CompileAs>
      <StringPooling>true</StringPooling>
      <MinimalRebuild>false</MinimalRebuild>
      <DisableSpecificWarnings>4996;4127</DisableSpecificWarnings>
      <AdditionalIncludeDirectories>$(ZLibSrcDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <TreatWarningAsError>true</TreatWarningAsError>
      <Optimization>Full</Optimization>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
    <Lib>
      <LinkTimeCodeGeneration>true</LinkTimeCodeGeneration>
    </Lib>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="..\..\..\png.c">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug Library|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release Library|Win32'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="..\..\..\pngerror.c" />
    <ClCompile Include="..\..\..\pngget.c" />
    <ClCompile Include="..\..\..\pngmem.c" />
    <ClCompile Include="..\..\..\pngpread.c" />
    <ClCompile Include="..\..\..\pngread.c" />
    <ClCompile Include="..\..\..\pngrio.c" />
    <ClCompile Include="..\..\..\pngrtran.c" />
    <ClCompile Include="..\..\..\pngrutil.c" />
    <ClCompile Include="..\..\..\pngset.c" />
    <ClCompile Include="..\..\..\pngtrans.c" />
    <ClCompile Include="..\..\..\pngwio.c" />
    <ClCompile Include="..\..\..\pngwrite.c" />
    <ClCompile Include="..\..\..\pngwtran.c" />
    <ClCompile Include="..\..\..\pngwutil.c" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="..\..\..\scripts\pngwin.rc">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug Library|Win32'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release Library|Win32'">true</ExcludedFromBuild>
    </ResourceCompile>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>
