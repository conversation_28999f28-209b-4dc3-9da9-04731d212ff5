# Amiga powerUP (TM) Makefile
# makefile for libpng and SAS C V6.58/7.00 PPC compiler
# Copyright (C) 1998 by <PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

CC       = scppc
CFLAGS   = NOSTKCHK NOSINT OPTIMIZE OPTGO OPTPEEP OPTINLOCAL OPTINL IDIR /zlib \
           OPTLOOP OPTRDEP=8 OPTDEP=8 OPTCOMP=8
LIBNAME  = libpng.a
AR       = ppc-amigaos-ar
AR_FLAGS = cr
RANLIB   = ppc-amigaos-ranlib
LDFLAGS  = -r -o
LDLIBS   =  ../zlib/libzip.a LIB:scppc.a
LN       = ppc-amigaos-ld
RM       = delete quiet
MKDIR    = makedir

OBJS = png.o pngset.o pngget.o pngrutil.o pngtrans.o pngwutil.o pngread.o \
       pngerror.o pngpread.o pngwrite.o pngrtran.o pngwtran.o pngrio.o \
       pngwio.o pngmem.o

all: $(LIBNAME) pngtest

$(LIBNAME): $(OBJS)
            $(AR) $(AR_FLAGS) $@ $(OBJS)
            $(RANLIB) $@

pngtest: pngtest.o $(LIBNAME)
        $(LN) $(LDFLAGS) pngtest LIB:c_ppc.o pngtest.o $(LIBNAME) $(LDLIBS) \
LIB:end.o
