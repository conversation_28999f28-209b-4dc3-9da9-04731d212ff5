40
targetIdent
0
MProject
1
MComponent
0
2
WString
4
NEXE
3
WString
5
nc2en
1
0
0
4
MCommand
0
5
MCommand
1118
pngstest --strict --log ../../contrib/pngsuite/basn0g01.png ../../contrib/pngsuite/basn0g02.png ../../contrib/pngsuite/basn0g04.png ../../contrib/pngsuite/basn0g08.png ../../contrib/pngsuite/basn0g16.png ../../contrib/pngsuite/basn2c08.png ../../contrib/pngsuite/basn2c16.png ../../contrib/pngsuite/basn3p01.png ../../contrib/pngsuite/basn3p02.png ../../contrib/pngsuite/basn3p04.png ../../contrib/pngsuite/basn3p08.png ../../contrib/pngsuite/basn4a08.png ../../contrib/pngsuite/basn4a16.png ../../contrib/pngsuite/basn6a08.png ../../contrib/pngsuite/basn6a16.png ../../contrib/pngsuite/ftbbn0g04.png ../../contrib/pngsuite/ftbbn0g01.png ../../contrib/pngsuite/ftbbn0g02.png ../../contrib/pngsuite/ftbbn2c16.png ../../contrib/pngsuite/ftbbn3p08.png ../../contrib/pngsuite/ftbgn2c16.png ../../contrib/pngsuite/ftbgn3p08.png ../../contrib/pngsuite/ftbrn2c08.png ../../contrib/pngsuite/ftbwn0g16.png ../../contrib/pngsuite/ftbwn3p08.png ../../contrib/pngsuite/ftbyn3p08.png ../../contrib/pngsuite/ftp0n0g08.png ../../contrib/pngsuite/ftp0n2c08.png ../../contrib/pngsuite/ftp0n3p08.png ../../contrib/pngsuite/ftp1n3p08.png
6
MItem
12
pngstest.exe
7
WString
4
NEXE
8
WVList
6
9
MVState
10
WString
7
WINLINK
11
WString
11
?????Stack:
1
12
WString
4
768k
0
13
MVState
14
WString
7
WINLINK
15
WString
28
?????Library directories(;):
1
16
WString
8
$(%zlib)
0
17
MVState
18
WString
7
WINLINK
19
WString
18
?????Libraries(,):
1
20
WString
19
libpng.lib zlib.lib
0
21
MVState
22
WString
7
WINLINK
23
WString
11
?????Stack:
0
24
WString
4
768k
0
25
MVState
26
WString
7
WINLINK
27
WString
28
?????Library directories(;):
0
28
WString
8
$(%zlib)
0
29
MVState
30
WString
7
WINLINK
31
WString
18
?????Libraries(,):
0
32
WString
19
libpng.lib zlib.lib
0
33
WVList
1
34
ActionStates
35
WString
4
&Run
36
WVList
0
-1
1
1
0
37
WPickList
2
38
MItem
3
*.c
39
WString
4
COBJ
40
WVList
2
41
MVState
42
WString
3
WCC
43
WString
25
n????Include directories:
1
44
WString
39
"$(%zlib);$(%watcom)/h;$(%watcom)/h/nt"
0
45
MVState
46
WString
3
WCC
47
WString
25
n????Include directories:
0
48
WString
39
"$(%zlib);$(%watcom)/h;$(%watcom)/h/nt"
0
49
WVList
0
-1
1
1
0
50
MItem
33
..\..\contrib\libtests\pngstest.c
51
WString
4
COBJ
52
WVList
0
53
WVList
0
38
1
1
0
