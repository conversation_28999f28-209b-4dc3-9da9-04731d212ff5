# Makefile for libpng
# 32-bit Borland C++ (Note: All modules are compiled in C mode)
# To build the library, do:
#       "make -fmakefile.bc32"
#
# -------------------- 32-bit Borland C++ --------------------

### Absolutely necessary for this makefile to work
.AUTODEPEND

## Where zlib.h, zconf.h and zlib.lib are
ZLIB_DIR=..\zlib

## Compiler, linker and lib stuff
CC=bcc32
LD=bcc32
LIB=tlib

# -3 = 386, -4 = 486, -5 = Pentium etc.
!ifndef TARGET_CPU
#TARGET_CPU=-6
!endif

# Use this if you don't want <PERSON><PERSON>'s fancy exception handling
# (Caution: doesn't work with CBuilderX)
#NOEHLIB=noeh32.lib

!ifdef DEBUG
CDEBUG=-v
LDEBUG=-v
!else
CDEBUG=
LDEBUG=
!endif

# STACKOFLOW=1
!ifdef STACKOFLOW
CDEBUG=$(CDEBUG) -N
LDEBUG=$(LDEBUG) -N
!endif

# -O2 optimize for speed
# -d  merge duplicate strings
# -k- turn off standard stack frame
# -w  display all warnings
CFLAGS=-I$(ZLIB_DIR) -O2 -d -k- -w $(TARGET_CPU) $(CDEBUG)

# -M  generate map file
LDFLAGS=-L$(ZLIB_DIR) -M $(LDEBUG)

## Variables
OBJS = \
	png.obj \
	pngerror.obj \
	pngget.obj \
	pngmem.obj \
	pngpread.obj \
	pngread.obj \
	pngrio.obj \
	pngrtran.obj \
	pngrutil.obj \
	pngset.obj \
	pngtrans.obj \
	pngwio.obj \
	pngwrite.obj \
	pngwtran.obj \
	pngwutil.obj

LIBOBJS = \
	+png.obj \
	+pngerror.obj \
	+pngget.obj \
	+pngmem.obj \
	+pngpread.obj \
	+pngread.obj \
	+pngrio.obj \
	+pngrtran.obj \
	+pngrutil.obj \
	+pngset.obj \
	+pngtrans.obj \
	+pngwio.obj \
	+pngwrite.obj \
	+pngwtran.obj \
	+pngwutil.obj

LIBNAME=libpng.lib

## Implicit rules
# Braces let make "batch" calls to the compiler,
# 2 calls instead of 12; space is important.
.c.obj:
	$(CC) $(CFLAGS) -c {$*.c }

.c.exe:
	$(CC) $(CFLAGS) $(LDFLAGS) $*.c $(LIBNAME) zlib.lib $(NOEHLIB)

.obj.exe:
	$(LD) $(LDFLAGS) $*.obj $(LIBNAME) zlib.lib $(NOEHLIB)

## Major targets
all: libpng pngtest

libpng: $(LIBNAME)

pngtest: pngtest.exe

test: pngtest.exe
	pngtest

## Minor Targets

# see scripts\pnglibconf.mak for how to make this file
# with different options
pnglibconf.h: scripts\pnglibconf.h.prebuilt
	copy scripts\pnglibconf.h.prebuilt $@

png.obj: png.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngerror.obj: pngerror.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngget.obj: pngget.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngmem.obj: pngmem.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngpread.obj: pngpread.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngread.obj: pngread.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrio.obj: pngrio.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrtran.obj: pngrtran.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrutil.obj: pngrutil.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngset.obj: pngset.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngtrans.obj: pngtrans.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwio.obj: pngwio.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwrite.obj: pngwrite.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwtran.obj: pngwtran.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwutil.obj: pngwutil.c png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngtest.obj: pngtest.c png.h pngconf.h pnglibconf.h

$(LIBNAME): $(OBJS)
	-del $(LIBNAME)
	$(LIB) $(LIBNAME) @&&|
$(LIBOBJS), libpng
|

# Cleanup
clean:
	-del pnglibconf.h
	-del *.obj
	-del $(LIBNAME)
	-del pngtest.exe
	-del *.lst
	-del *.map
	-del *.tds
	-del pngout.png

# End of makefile for libpng
