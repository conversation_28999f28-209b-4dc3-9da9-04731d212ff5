/*
  Simple DirectMedia Layer
  Copyright (C) 1997-2016 Sam <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.
*/
#include "SDL_config.h"

#include "SDL_test.h"

/* GIMP RGB C-Source image dump (primitives.c) */

const SDLTest_SurfaceImage_t SDLTest_imagePrimitives = {
  80, 60, 3,
  "\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15"
  "I\310\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310"
  "\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0"
  "\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0\0\0\15I\310\0\0"
  "\0\5ii\0\0\0\5ii\0\0\0\3\1\1\0\0\0\5\2\1\0\0\0\7\3\2\0\0\0\11\4\3\0\0\0\13"
  "\5\3\0\0\0\15\6\4\0\0\0\17\7\5\0\0\0\21\10\5\0\0\0\23\11\6\0\0\0\25\12\7"
  "\0\0\0\27\13\7\0\0\0\31\14\10\0\0\0\33\15\11\0\0\0\35\16\11\0\0\0\37\17\12"
  "\0\0\0!\20\13\0\0\0#\21\13\0\0\0%\22\14\0\0\0'\23\15\15I\310)\24\15\15I\310"
  "+\25\16\15I\310-\26\17\15I\310/\27\17\15I\3101\30\20\15I\3103\31\21\15I\310"
  "5\32\21\15I\3107\33\22\15I\3109\34\23\15I\310;\35\23\15I\310=\36\24\15I\310"
  "?\37\25\15I\310A\40\25\15I\310C!\26\15I\310E\"\27\15I\310G#\27\15I\310I$"
  "\30\15I\310K%\31\15I\310M&\31\5iiO'\32\0\0\0\0\0\0\5ii\0\0\0\10\4\2\0\0\0"
  "\14\6\4\0\0\0\20\10\5\0\0\0\24\12\6\0\0\0\30\14\10\0\0\0\34\16\11\0\0\0\40"
  "\20\12\0\0\0$\22\14\0\0\0(\24\15\0\0\0,\26\16\0\0\0""0\30\20\0\0\0""4\32"
  "\21\0\0\0""8\34\22\0\0\0<\36\24\0\0\0@\40\25\0\0\0D\"\26\0\0\0H$\30\0\0\0"
  "L&\31\0\0\0P(\32\15I\310T*\34\15I\310X,\35\15I\310\\.\36\15I\310`0\40\15"
  "I\310d2!\15I\310h4\"\15I\310l6$\15I\310p8%\15I\310t:&\15I\310x<(\15I\310"
  "|>)\15I\310\200@*\15I\310\204B,\15I\310\210D-\15I\310\214F.\15I\310\220H"
  "0\15I\310\224J1\15I\310\230L2\5ii\234N4\15I\310\0\0\0\0\0\0\0\0\0\5ii\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\5ii"
  "\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\5ii\15I\310\15I\310\15I\310\15I\310"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\5ii\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\5ii\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\5ii\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\5ii\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\5ii\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d"
  "\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\5ii\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\5ii\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d"
  "\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\5ii\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310"
  "\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d"
  "\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d"
  "\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5"
  "ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d"
  "\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d"
  "\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0"
  "\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0"
  "\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0"
  "\0\377\0\0\377\0\0\377\0\0\377\0\5ii\0\377\0\0\377\0\0\377\0\0\377\0\0\377"
  "\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0""77\5\0\377\0\0\377\0\0\377\0"
  "\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\5ii\0\377\0\0\377\0\0\377"
  "\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377"
  "\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377"
  "\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\377\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5"
  "ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d"
  "\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5i"
  "i\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310"
  "\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\310\0d\310\0d\5ii\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310"
  "\0d\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\310\0d\310\0d\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d77\5\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\5ii\310\0d\310\0d\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\310\0d\5ii\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d77\5\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\5ii\310\0d\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15"
  "I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\5ii\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d7"
  "7\5\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0"
  "d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310"
  "\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\310\0d\5ii\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0""77\5\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\5ii\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0""77\5\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\5ii\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0""77\5\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\5ii\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0""77\5\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\5ii\15I\310"
  "\15I\310\15I\310\15I\310\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\5"
  "ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0""77\5\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\5ii\15I\310\15I\310\15I\310"
  "\15I\310\15I\310\0\0\0\0\0\0\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0""77\5\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\5ii\15I\310\15I\310\15I\310\15I\310\0\0\0\0"
  "\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0""77\5\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\5ii\15I\310\15I\310\15I\310\0\0\0\0\0\0\5ii\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0""77\5\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\5ii"
  "\15I\310\15I\310\0\0\0\5ii\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0""77\5\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\5ii\15I\310\5ii\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"
  "\0\0\0\0\0\0\0""77\5\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I\310\15I"
  "\310\15I\310\15I\310\15I\310\15I\310\5ii",
};

/**
 * \brief Returns the Primitives test image as SDL_Surface.
 */
SDL_Surface *SDLTest_ImagePrimitives()
{
   SDL_Surface *surface = SDL_CreateRGBSurfaceFrom(
        (void*)SDLTest_imagePrimitives.pixel_data,
        SDLTest_imagePrimitives.width,
        SDLTest_imagePrimitives.height,
        SDLTest_imagePrimitives.bytes_per_pixel * 8,
        SDLTest_imagePrimitives.width * SDLTest_imagePrimitives.bytes_per_pixel,
#if (SDL_BYTEORDER == SDL_BIG_ENDIAN)
         0xff000000, /* Red bit mask. */
         0x00ff0000, /* Green bit mask. */
         0x0000ff00, /* Blue bit mask. */
         0x000000ff  /* Alpha bit mask. */
#else
         0x000000ff, /* Red bit mask. */
         0x0000ff00, /* Green bit mask. */
         0x00ff0000, /* Blue bit mask. */
         0xff000000  /* Alpha bit mask. */
#endif
         );
   return surface;
}
