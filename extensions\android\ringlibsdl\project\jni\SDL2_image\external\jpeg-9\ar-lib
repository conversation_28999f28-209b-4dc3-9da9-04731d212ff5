#! /bin/sh
# Wrapper for Microsoft lib.exe

me=ar-lib
scriptversion=2012-03-01.08; # UTC

# Copyright (C) 2010-2013 Free Software Foundation, Inc.
# Written by <PERSON> <<EMAIL>>.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2, or (at your option)
# any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

# As a special exception to the GNU General Public License, if you
# distribute this file as part of a program that contains a
# configuration script generated by Autoconf, you may include it under
# the same distribution terms that you use for the rest of that program.

# This file is maintained in Automake, please report
# <AUTHOR> <EMAIL> or send patches to
# <<EMAIL>>.


# func_error message
func_error ()
{
  echo "$me: $1" 1>&2
  exit 1
}

file_conv=

# func_file_conv build_file
# Convert a $build file to $host form and store it in $file
# Currently only supports Windows hosts.
func_file_conv ()
{
  file=$1
  case $file in
    / | /[!/]*) # absolute file, and not a UNC file
      if test -z "$file_conv"; then
	# lazily determine how to convert abs files
	case `uname -s` in
	  MINGW*)
	    file_conv=mingw
	    ;;
	  CYGWIN*)
	    file_conv=cygwin
	    ;;
	  *)
	    file_conv=wine
	    ;;
	esac
      fi
      case $file_conv in
	mingw)
	  file=`cmd //C echo "$file " | sed -e 's/"\(.*\) " *$/\1/'`
	  ;;
	cygwin)
	  file=`cygpath -m "$file" || echo "$file"`
	  ;;
	wine)
	  file=`winepath -w "$file" || echo "$file"`
	  ;;
      esac
      ;;
  esac
}

# func_at_file at_file operation archive
# Iterate over all members in AT_FILE performing OPERATION on ARCHIVE
# for each of them.
# When interpreting the content of the @FILE, do NOT use func_file_conv,
# since the user would need to supply preconverted file names to
# binutils ar, at least for MinGW.
func_at_file ()
{
  operation=$2
  archive=$3
  at_file_contents=`cat "$1"`
  eval set x "$at_file_contents"
  shift

  for member
  do
    $AR -NOLOGO $operation:"$member" "$archive" || exit $?
  done
}

case $1 in
  '')
     func_error "no command.  Try '$0 --help' for more information."
     ;;
  -h | --h*)
    cat <<EOF
Usage: $me [--help] [--version] PROGRAM ACTION ARCHIVE [MEMBER...]

Members may be specified in a file named with @FILE.
EOF
    exit $?
    ;;
  -v | --v*)
    echo "$me, version $scriptversion"
    exit $?
    ;;
esac

if test $# -lt 3; then
  func_error "you must specify a program, an action and an archive"
fi

AR=$1
shift
while :
do
  if test $# -lt 2; then
    func_error "you must specify a program, an action and an archive"
  fi
  case $1 in
    -lib | -LIB \
    | -ltcg | -LTCG \
    | -machine* | -MACHINE* \
    | -subsystem* | -SUBSYSTEM* \
    | -verbose | -VERBOSE \
    | -wx* | -WX* )
      AR="$AR $1"
      shift
      ;;
    *)
      action=$1
      shift
      break
      ;;
  esac
done
orig_archive=$1
shift
func_file_conv "$orig_archive"
archive=$file

# strip leading dash in $action
action=${action#-}

delete=
extract=
list=
quick=
replace=
index=
create=

while test -n "$action"
do
  case $action in
    d*) delete=yes  ;;
    x*) extract=yes ;;
    t*) list=yes    ;;
    q*) quick=yes   ;;
    r*) replace=yes ;;
    s*) index=yes   ;;
    S*)             ;; # the index is always updated implicitly
    c*) create=yes  ;;
    u*)             ;; # TODO: don't ignore the update modifier
    v*)             ;; # TODO: don't ignore the verbose modifier
    *)
      func_error "unknown action specified"
      ;;
  esac
  action=${action#?}
done

case $delete$extract$list$quick$replace,$index in
  yes,* | ,yes)
    ;;
  yesyes*)
    func_error "more than one action specified"
    ;;
  *)
    func_error "no action specified"
    ;;
esac

if test -n "$delete"; then
  if test ! -f "$orig_archive"; then
    func_error "archive not found"
  fi
  for member
  do
    case $1 in
      @*)
        func_at_file "${1#@}" -REMOVE "$archive"
        ;;
      *)
        func_file_conv "$1"
        $AR -NOLOGO -REMOVE:"$file" "$archive" || exit $?
        ;;
    esac
  done

elif test -n "$extract"; then
  if test ! -f "$orig_archive"; then
    func_error "archive not found"
  fi
  if test $# -gt 0; then
    for member
    do
      case $1 in
        @*)
          func_at_file "${1#@}" -EXTRACT "$archive"
          ;;
        *)
          func_file_conv "$1"
          $AR -NOLOGO -EXTRACT:"$file" "$archive" || exit $?
          ;;
      esac
    done
  else
    $AR -NOLOGO -LIST "$archive" | sed -e 's/\\/\\\\/g' | while read member
    do
      $AR -NOLOGO -EXTRACT:"$member" "$archive" || exit $?
    done
  fi

elif test -n "$quick$replace"; then
  if test ! -f "$orig_archive"; then
    if test -z "$create"; then
      echo "$me: creating $orig_archive"
    fi
    orig_archive=
  else
    orig_archive=$archive
  fi

  for member
  do
    case $1 in
    @*)
      func_file_conv "${1#@}"
      set x "$@" "@$file"
      ;;
    *)
      func_file_conv "$1"
      set x "$@" "$file"
      ;;
    esac
    shift
    shift
  done

  if test -n "$orig_archive"; then
    $AR -NOLOGO -OUT:"$archive" "$orig_archive" "$@" || exit $?
  else
    $AR -NOLOGO -OUT:"$archive" "$@" || exit $?
  fi

elif test -n "$list"; then
  if test ! -f "$orig_archive"; then
    func_error "archive not found"
  fi
  $AR -NOLOGO -LIST "$archive" || exit $?
fi
