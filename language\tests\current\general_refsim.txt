===================================
               MEM1
===================================
Var  S    RC LO Value               
-----------------------------------
a    LIVE 1  0  [1,2,3]             
mix  LIVE 3  0  [1,2,3,a,mix,mix]   
mix2 LIVE 3  0  mix                 
===================================
               MEM2
===================================
Var  S    RC LO Value               
-----------------------------------
a    LIVE 1  0  [b]                 
b    LIVE 1  0  [a]                 
===================================
               MEM3
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LIVE 1  0  [n2]                
n2   LIVE 2  0  [n1,n3]             
n3   LIVE 2  0  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
               MEM4
===================================
Var  S    RC LO Value               
-----------------------------------
x    LIVE 2  0  [1,2,3]             
mix  LIVE 3  0  [1,2,3,x,mix,mix]   
n1   LIVE 1  0  [mix,n2]            
n2   LIVE 2  0  [n1,n3]             
n3   LIVE 2  0  [n2,n4,x]           
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4,a]              
a    LIVE 2  0  [b]                 
b    LIVE 1  0  [a]                 
===================================
               MEM5
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LIVE 1  0  [n2]                
n2   LIVE 1  0  [n3]                
n3   LIVE 1  0  [n4]                
n4   LIVE 1  0  [n5]                
n5   LIVE 1  0  [n1]                
===================================
         Test: GetChildren
===================================
MEM1 - a    : []
MEM1 - mix  : [a,mix,mix]
MEM1 - mix2 : [mix]
MEM2 - a    : [b]
MEM2 - b    : [a]
MEM3 - n1   : [n2]
MEM3 - n2   : [n1,n3]
MEM3 - n3   : [n2,n4]
MEM3 - n4   : [n3,n5]
MEM3 - n5   : [n4]
===================================
      Test: GetNestedChildren
===================================
MEM1 - a    : []
MEM1 - mix  : [a,mix,mix]
MEM1 - mix2 : [mix,a]
MEM2 - a    : [b,a]
MEM2 - b    : [a,b]
MEM3 - n1   : [n2,n1,n3,n4,n5]
MEM3 - n2   : [n1,n3,n2,n4,n5]
MEM3 - n3   : [n2,n4,n1,n3,n5]
MEM3 - n4   : [n3,n5,n2,n4,n1]
MEM3 - n5   : [n4,n3,n5,n2,n1]
===================================
    Test: Direct Cirular Count
===================================
MEM1 - a    : 0
MEM1 - mix  : 2
MEM1 - mix2 : 2
MEM2 - a    : 0
MEM2 - b    : 0
MEM3 - n1   : 0
MEM3 - n2   : 0
MEM3 - n3   : 0
MEM3 - n4   : 0
MEM3 - n5   : 0
===================================
   Test: Indirect Cirular Count
===================================
MEM1 - a    : 0
MEM1 - mix  : 0
MEM1 - mix2 : 0
MEM2 - a    : 1
MEM2 - b    : 1
MEM3 - n1   : 1
MEM3 - n2   : 2
MEM3 - n3   : 2
MEM3 - n4   : 2
MEM3 - n5   : 1
===================================
               MEM1
===================================
Var  S    RC LO Value               
-----------------------------------
a    LIVE 1  0  [1,2,3]             
mix  LIVE 3  0  [1,2,3,a,mix,mix]   
mix2 LIVE 3  0  mix                 
===================================
      Test deleteVar(MEM1,:a)
===================================
Var  S    RC LO Value               
-----------------------------------
a    LOST 0  1  [1,2,3]             
mix  LIVE 3  0  [1,2,3,a,mix,mix]   
mix2 LIVE 3  0  mix                 
===================================
     Test deleteVar(MEM1,:mix)
===================================
Var  S    RC LO Value               
-----------------------------------
a    LOST 0  2  [1,2,3]             
mix  LOST 2  1  [1,2,3,a,mix,mix]   
mix2 LIVE 2  0  mix                 
===================================
    Test deleteVar(MEM1,:mix2)
===================================
Var  S    RC LO Value               
-----------------------------------
a    DEAD 0  2  [1,2,3]             
mix  DEAD 0  1  [1,2,3,a,mix,mix]   
mix2 DEAD 0  0  mix                 
[No Memory Leak]
===================================
               MEM2
===================================
Var  S    RC LO Value               
-----------------------------------
a    LIVE 1  0  [b]                 
b    LIVE 1  0  [a]                 
===================================
      Test deleteVar(MEM2,:a)
===================================
Var  S    RC LO Value               
-----------------------------------
a    LOST 0  1  [b]                 
b    LIVE 1  1  [a]                 
===================================
      Test deleteVar(MEM2,:b)
===================================
Var  S    RC LO Value               
-----------------------------------
a    DEAD 0  3  [b]                 
b    DEAD 0  2  [a]                 
[No Memory Leak]
===================================
               MEM3
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LIVE 1  0  [n2]                
n2   LIVE 2  0  [n1,n3]             
n3   LIVE 2  0  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n1)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  1  [n2]                
n2   LIVE 2  1  [n1,n3]             
n3   LIVE 2  0  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n2)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  2  [n2]                
n2   LOST 1  2  [n1,n3]             
n3   LIVE 2  1  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n3)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  2  [n2]                
n2   LOST 1  3  [n1,n3]             
n3   LOST 1  2  [n2,n4]             
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n4)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  2  [n2]                
n2   LOST 1  3  [n1,n3]             
n3   LOST 1  3  [n2,n4]             
n4   LOST 1  2  [n3,n5]             
n5   LIVE 1  1  [n4]                
===================================
     Test deleteVar(MEM3,:n5)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   DEAD 0  3  [n2]                
n2   DEAD 0  4  [n1,n3]             
n3   DEAD 0  4  [n2,n4]             
n4   DEAD 0  4  [n3,n5]             
n5   DEAD 0  2  [n4]                
[No Memory Leak]
===================================
               MEM3
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LIVE 1  0  [n2]                
n2   LIVE 2  0  [n1,n3]             
n3   LIVE 2  0  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n3)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LIVE 1  0  [n2]                
n2   LIVE 2  1  [n1,n3]             
n3   LOST 1  1  [n2,n4]             
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n2)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LIVE 1  1  [n2]                
n2   LOST 1  2  [n1,n3]             
n3   LOST 1  2  [n2,n4]             
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n1)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  2  [n2]                
n2   LOST 1  3  [n1,n3]             
n3   LOST 1  2  [n2,n4]             
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n4)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  2  [n2]                
n2   LOST 1  3  [n1,n3]             
n3   LOST 1  3  [n2,n4]             
n4   LOST 1  2  [n3,n5]             
n5   LIVE 1  1  [n4]                
===================================
     Test deleteVar(MEM3,:n5)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   DEAD 0  3  [n2]                
n2   DEAD 0  4  [n1,n3]             
n3   DEAD 0  4  [n2,n4]             
n4   DEAD 0  4  [n3,n5]             
n5   DEAD 0  2  [n4]                
[No Memory Leak]
===================================
               MEM3
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LIVE 1  0  [n2]                
n2   LIVE 2  0  [n1,n3]             
n3   LIVE 2  0  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(mem3,:n1)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  1  [n2]                
n2   LIVE 2  1  [n1,n3]             
n3   LIVE 2  0  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
    Test deleteItem(mem3,:n2,1)
===================================
===================================
               MEM3
===================================
Var  S    RC LO Value               
-----------------------------------
n1   DEAD 0  2  [n2]                
n2   LIVE 2  1  [n3]                
n3   LIVE 2  0  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n2)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   DEAD 0  2  [n2]                
n2   LOST 1  2  [n3]                
n3   LIVE 2  1  [n2,n4]             
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n3)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   DEAD 0  2  [n2]                
n2   LOST 1  3  [n3]                
n3   LOST 1  2  [n2,n4]             
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4]                
===================================
     Test deleteVar(MEM3,:n4)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   DEAD 0  2  [n2]                
n2   LOST 1  3  [n3]                
n3   LOST 1  3  [n2,n4]             
n4   LOST 1  2  [n3,n5]             
n5   LIVE 1  1  [n4]                
===================================
     Test deleteVar(MEM3,:n5)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   DEAD 0  2  [n2]                
n2   DEAD 0  4  [n3]                
n3   DEAD 0  4  [n2,n4]             
n4   DEAD 0  4  [n3,n5]             
n5   DEAD 0  2  [n4]                
[No Memory Leak]
===================================
               MEM4
===================================
Var  S    RC LO Value               
-----------------------------------
x    LIVE 2  0  [1,2,3]             
mix  LIVE 3  0  [1,2,3,x,mix,mix]   
n1   LIVE 1  0  [mix,n2]            
n2   LIVE 2  0  [n1,n3]             
n3   LIVE 2  0  [n2,n4,x]           
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4,a]              
a    LIVE 2  0  [b]                 
b    LIVE 1  0  [a]                 
===================================
     Test deleteVar(MEM4,:n1)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LIVE 2  0  [1,2,3]             
mix  LIVE 3  1  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  1  [n1,n3]             
n3   LIVE 2  0  [n2,n4,x]           
n4   LIVE 2  0  [n3,n5]             
n5   LIVE 1  0  [n4,a]              
a    LIVE 2  0  [b]                 
b    LIVE 1  0  [a]                 
===================================
     Test deleteVar(MEM4,:n3)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LIVE 2  1  [1,2,3]             
mix  LIVE 3  1  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  2  [n1,n3]             
n3   LOST 1  1  [n2,n4,x]           
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4,a]              
a    LIVE 2  0  [b]                 
b    LIVE 1  0  [a]                 
===================================
      Test deleteVar(MEM4,:x)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LOST 1  2  [1,2,3]             
mix  LIVE 3  1  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  2  [n1,n3]             
n3   LOST 1  1  [n2,n4,x]           
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4,a]              
a    LIVE 2  0  [b]                 
b    LIVE 1  0  [a]                 
===================================
     Test deleteVar(MEM4,:mix)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LOST 1  3  [1,2,3]             
mix  LOST 2  2  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  2  [n1,n3]             
n3   LOST 1  1  [n2,n4,x]           
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4,a]              
a    LIVE 2  0  [b]                 
b    LIVE 1  0  [a]                 
===================================
      Test deleteVar(MEM4,:b)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LOST 1  3  [1,2,3]             
mix  LOST 2  2  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  2  [n1,n3]             
n3   LOST 1  1  [n2,n4,x]           
n4   LIVE 2  1  [n3,n5]             
n5   LIVE 1  0  [n4,a]              
a    LIVE 2  1  [b]                 
b    LOST 0  1  [a]                 
===================================
     Test deleteVar(MEM4,:n5)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LOST 1  3  [1,2,3]             
mix  LOST 2  2  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  2  [n1,n3]             
n3   LOST 1  1  [n2,n4,x]           
n4   LIVE 2  2  [n3,n5]             
n5   LOST 0  1  [n4,a]              
a    LIVE 2  2  [b]                 
b    LOST 0  1  [a]                 
===================================
      Test deleteVar(MEM4,:a)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LOST 1  3  [1,2,3]             
mix  LOST 2  2  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  2  [n1,n3]             
n3   LOST 1  1  [n2,n4,x]           
n4   LIVE 2  2  [n3,n5]             
n5   LOST 0  1  [n4,a]              
a    LOST 1  3  [b]                 
b    LOST 0  2  [a]                 
===================================
     Test deleteVar(MEM4,:n3)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LOST 1  4  [1,2,3]             
mix  LOST 2  2  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  3  [n1,n3]             
n3   LOST 0  2  [n2,n4,x]           
n4   LIVE 2  3  [n3,n5]             
n5   LOST 0  1  [n4,a]              
a    LOST 1  3  [b]                 
b    LOST 0  2  [a]                 
===================================
     Test deleteVar(MEM4,:n4)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LOST 1  4  [1,2,3]             
mix  LOST 2  2  [1,2,3,x,mix,mix]   
n1   LOST 0  1  [mix,n2]            
n2   LIVE 2  3  [n1,n3]             
n3   LOST 0  3  [n2,n4,x]           
n4   LOST 1  4  [n3,n5]             
n5   LOST 0  2  [n4,a]              
a    LOST 1  3  [b]                 
b    LOST 0  2  [a]                 
===================================
     Test deleteVar(MEM4,:n2)
===================================
Var  S    RC LO Value               
-----------------------------------
x    LOST 1  4  [1,2,3]             
mix  LOST 2  2  [1,2,3,x,mix,mix]   
n1   LOST 0  2  [mix,n2]            
n2   LOST 1  4  [n1,n3]             
n3   LOST 0  4  [n2,n4,x]           
n4   LOST 1  4  [n3,n5]             
n5   LOST 0  2  [n4,a]              
a    LOST 1  3  [b]                 
b    LOST 0  2  [a]                 
[Memory Leak Detected!]
===================================
               MEM5
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LIVE 1  0  [n2]                
n2   LIVE 1  0  [n3]                
n3   LIVE 1  0  [n4]                
n4   LIVE 1  0  [n5]                
n5   LIVE 1  0  [n1]                
===================================
     Test deleteVar(MEM5,:n1)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  1  [n2]                
n2   LIVE 1  1  [n3]                
n3   LIVE 1  0  [n4]                
n4   LIVE 1  0  [n5]                
n5   LIVE 1  0  [n1]                
===================================
     Test deleteVar(MEM5,:n2)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  1  [n2]                
n2   LOST 0  2  [n3]                
n3   LIVE 1  1  [n4]                
n4   LIVE 1  0  [n5]                
n5   LIVE 1  0  [n1]                
===================================
     Test deleteVar(MEM5,:n3)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  1  [n2]                
n2   LOST 0  2  [n3]                
n3   LOST 0  2  [n4]                
n4   LIVE 1  1  [n5]                
n5   LIVE 1  0  [n1]                
===================================
     Test deleteVar(MEM5,:n4)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   LOST 0  1  [n2]                
n2   LOST 0  2  [n3]                
n3   LOST 0  2  [n4]                
n4   LOST 0  2  [n5]                
n5   LIVE 1  1  [n1]                
===================================
     Test deleteVar(MEM5,:n5)
===================================
Var  S    RC LO Value               
-----------------------------------
n1   DEAD 0  3  [n2]                
n2   DEAD 0  3  [n3]                
n3   DEAD 0  3  [n4]                
n4   DEAD 0  3  [n5]                
n5   DEAD 0  2  [n1]                
[No Memory Leak]
