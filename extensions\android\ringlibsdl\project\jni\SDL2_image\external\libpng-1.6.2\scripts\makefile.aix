# makefile for libpng using gcc (generic, static library)
# Copyright (C) 2002, 2006-2009 <PERSON>-<PERSON><PERSON><PERSON>
# Copyright (C) 2000 Cosmin Truta
# Copyright (C) 2000 <PERSON>lo<PERSON> (AIX support added, from makefile.gcc)
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

# Location of the zlib library and include files
ZLIBINC = ../zlib
ZLIBLIB = ../zlib

# Compiler, linker, lib and other tools
CC = gcc
LD = $(CC)
AR_RC = ar rcs
MKDIR_P = mkdir -p
RANLIB = ranlib
RM_F = rm -f
LN_SF = ln -f -s

LIBNAME=libpng16
PNGMAJ = 16

prefix=/usr/local
INCPATH=$(prefix)/include
LIBPATH=$(prefix)/lib

# override DESTDIR= on the make install command line to easily support
# installing into a temporary location.  Example:
#
#    make install DESTDIR=/tmp/build/libpng
#
# If you're going to install into a temporary location
# via DESTDIR, $(DESTDIR)$(prefix) must already exist before
# you execute make install.
DESTDIR=

DI=$(DESTDIR)$(INCPATH)
DL=$(DESTDIR)$(LIBPATH)

CDEBUG = -g -DPNG_DEBUG=5
LDDEBUG =
CRELEASE = -O2
LDRELEASE = -s
WARNMORE=-W -Wall
CFLAGS = -I$(ZLIBINC) $(WARNMORE) $(CRELEASE)
LDFLAGS = -L. -L$(ZLIBLIB) -lpng16 -lz -lm $(LDRELEASE)

# File extensions
O=.o
A=.a
E=

# Variables
OBJS =  png$(O) pngerror$(O) pngget$(O) pngmem$(O) pngpread$(O) \
	pngread$(O) pngrio$(O) pngrtran$(O) pngrutil$(O) pngset$(O) \
	pngtrans$(O) pngwio$(O) pngwrite$(O) pngwtran$(O) pngwutil$(O)

# Targets
all: $(LIBNAME)$(A) pngtest$(E)

include scripts/pnglibconf.mak
REMOVE = $(RM_F)
DFNFLAGS = $(DEFS) $(CPPFLAGS)

$(LIBNAME)$(A): $(OBJS)
	$(AR_RC) $@ $(OBJS)
	$(RANLIB) $@

test: pngtest$(E)
	./pngtest$(E)

pngtest$(E): pngtest$(O) $(LIBNAME)$(A)
	$(LD) -o $@ pngtest$(O) $(LDFLAGS)

install: $(LIBNAME)$(A)
	-@if [ ! -d $(DI)  ]; then $(MKDIR_P) $(DI); fi
	-@if [ ! -d $(DI)/$(LIBNAME)  ]; then $(MKDIR_P) $(DI)/$(LIBNAME); fi
	-@if [ ! -d $(DL) ]; then $(MKDIR_P) $(DL); fi
	-@$(RM_F) $(DI)/$(LIBNAME)/png.h
	-@$(RM_F) $(DI)/$(LIBNAME)/pngconf.h
	-@$(RM_F) $(DI)/$(LIBNAME)/pnglibconf.h
	-@$(RM_F) $(DI)/png.h
	-@$(RM_F) $(DI)/pngconf.h
	-@$(RM_F) $(DI)/pnglibconf.h
	cp png.h pngconf.h pnglibconf.h $(DI)/$(LIBNAME)
	chmod 644 $(DI)/$(LIBNAME)/png.h \
	$(DI)/$(LIBNAME)/pngconf.h \
	$(DI)/$(LIBNAME)/pnglibconf.h
	-@$(RM_F) -r $(DI)/libpng
	(cd $(DI); $(LN_SF) $(LIBNAME) libpng; $(LN_SF) $(LIBNAME)/* .)
	-@$(RM_F) $(DL)/$(LIBNAME)$(A)
	-@$(RM_F) $(DL)/libpng$(A)
	cp $(LIBNAME)$(A) $(DL)/$(LIBNAME)$(A)
	chmod 644 $(DL)/$(LIBNAME)$(A)
	(cd $(DL); $(LN_SF) $(LIBNAME)$(A) libpng$(A))
	(cd $(DI); $(LN_SF) libpng/* .;)

clean:
	$(RM_F) *.o $(LIBNAME)$(A) pngtest pngout.png pnglibconf.h

png$(O):      png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngerror$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngget$(O):   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngmem$(O):   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngpread$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngread$(O):  png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrio$(O):   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrtran$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngrutil$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngset$(O):   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngtrans$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwio$(O):   png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwrite$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwtran$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
pngwutil$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h

pngtest$(O):  png.h pngconf.h pnglibconf.h
