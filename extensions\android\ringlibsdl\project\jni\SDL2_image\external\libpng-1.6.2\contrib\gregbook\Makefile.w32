# Sample makefile for rpng-win / rpng2-win / wpng using MSVC and NMAKE.
# <PERSON>
# Last modified:  2 June 2007
#
#	The programs built by this makefile are described in the book,
#	"PNG:  The Definitive Guide," by <PERSON> (<PERSON><PERSON> and
#	Associates, 1999).  Go buy a copy, eh?  Well, OK, it's not
#	generally for sale anymore, but it's the thought that counts,
#	right?  (Hint:  http://www.libpng.org/pub/png/book/ )
#
# Invoke this makefile from a DOS prompt window via:
#
#	%devstudio%\vc\bin\vcvars32.bat
#	nmake -nologo -f Makefile.w32
#
# where %devstudio% is the installation directory for MSVC / DevStudio.  If
# you get "environment out of space" errors, create a desktop shortcut with
# "c:\windows\command.com /e:4096" as the program command line and set the
# working directory to this directory.  Then double-click to open the new
# DOS-prompt window with a bigger environment and retry the commands above.
#
# This makefile assumes libpng and zlib have already been built or downloaded
# and are in subdirectories at the same level as the current subdirectory
# (as indicated by the PNGPATH and ZPATH macros below).  Edit as appropriate.
#
# Note that the names of the dynamic and static libpng and zlib libraries
# used below may change in later releases of the libraries.  This makefile
# builds statically linked executables, but that can be changed by uncom-
# menting the appropriate PNGLIB and ZLIB lines.

!include <ntwin32.mak>


# macros --------------------------------------------------------------------

PNGPATH = ../libpng
PNGINC = -I$(PNGPATH)
#PNGLIB = $(PNGPATH)/pngdll.lib
PNGLIB = $(PNGPATH)/libpng.lib

ZPATH = ../zlib
ZINC = -I$(ZPATH)
#ZLIB = $(ZPATH)/zlibdll.lib
ZLIB = $(ZPATH)/zlibstat.lib

WINLIBS = -defaultlib:user32.lib gdi32.lib
# ["real" apps may also need comctl32.lib, comdlg32.lib, winmm.lib, etc.]

INCS = $(PNGINC) $(ZINC)
RLIBS = $(PNGLIB) $(ZLIB) $(WINLIBS)
WLIBS = $(PNGLIB) $(ZLIB)

CC = cl
LD = link
RM = del
CFLAGS = -nologo -O -W3 $(INCS) $(cvars)
# [note that -W3 is an MSVC-specific compilation flag ("all warnings on")]
# [see %devstudio%\vc\include\win32.mak for cvars macro definition]
O = .obj
E = .exe

RLDFLAGS = -nologo -subsystem:windows
WLDFLAGS = -nologo

RPNG  = rpng-win
RPNG2 = rpng2-win
WPNG  = wpng

ROBJS  = $(RPNG)$(O) readpng$(O)
ROBJS2 = $(RPNG2)$(O) readpng2$(O)
WOBJS  = $(WPNG)$(O) writepng$(O)

EXES = $(RPNG)$(E) $(RPNG2)$(E) $(WPNG)$(E)


# implicit make rules -------------------------------------------------------

.c$(O):
	$(CC) -c $(CFLAGS) $<


# dependencies --------------------------------------------------------------

all:  $(EXES)

$(RPNG)$(E): $(ROBJS)
	$(LD) $(RLDFLAGS) -out:$@ $(ROBJS) $(RLIBS)

$(RPNG2)$(E): $(ROBJS2)
	$(LD) $(RLDFLAGS) -out:$@ $(ROBJS2) $(RLIBS)

$(WPNG)$(E): $(WOBJS)
	$(LD) $(WLDFLAGS) -out:$@ $(WOBJS) $(WLIBS)

$(RPNG)$(O):	$(RPNG).c readpng.h
$(RPNG2)$(O):	$(RPNG2).c readpng2.h
$(WPNG)$(O):	$(WPNG).c writepng.h

readpng$(O):	readpng.c readpng.h
readpng2$(O):	readpng2.c readpng2.h
writepng$(O):	writepng.c writepng.h


# maintenance ---------------------------------------------------------------

clean:
#	ideally we could just do this:
#	$(RM) $(EXES) $(ROBJS) $(ROBJS2) $(WOBJS)
#	...but the Windows "DEL" command is none too bright, so:
	$(RM) r*$(E)
	$(RM) w*$(E)
	$(RM) r*$(O)
	$(RM) w*$(O)
