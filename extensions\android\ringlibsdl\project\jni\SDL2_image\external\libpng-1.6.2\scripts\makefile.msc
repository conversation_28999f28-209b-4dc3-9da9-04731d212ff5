# makefile for libpng
# Copyright (C) 1995 <PERSON>, Group 42, Inc.
# Copyright (C) 2006, 2009 <PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h
#
# Assumes that zlib.lib, zconf.h, and zlib.h have been copied to ..\zlib

# -------- Microsoft C 5.1 and later, does not use assembler code --------
MODEL=L
CFLAGS=-Oait -Gs -nologo -W3 -A$(MODEL) -I..\zlib
#-Ox generates bad code with MSC 5.1
CC=cl
LD=link
LDFLAGS=/e/st:0x1500/noe
O=.obj

#uncomment next to put error messages in a file
ERRFILE= >> pngerrs

# variables
OBJS1 = png$(O) pngset$(O) pngget$(O) pngrutil$(O) pngtrans$(O) pngwutil$(O)
OBJS2 = pngmem$(O) pngpread$(O) pngread$(O) pngerror$(O) pngwrite$(O)
OBJS3 = pngrtran$(O) pngwtran$(O) pngrio$(O) pngwio$(O)

all: libpng.lib

# see scripts/pnglibconf.mak for more options
pnglibconf.h: scripts/pnglibconf.h.prebuilt
	cp scripts/pnglibconf.h.prebuilt $@

png$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
		  $(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngset$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngget$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngread$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngpread$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
		  $(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngrtran$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngrutil$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngerror$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngmem$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngrio$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngwio$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngtrans$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngwrite$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngwtran$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngwutil$(O): png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

libpng.lib: $(OBJS1) $(OBJS2) $(OBJS3)
	del libpng.lib
	lib libpng $(OBJS1);
	lib libpng $(OBJS2);
	lib libpng $(OBJS3);

pngtest$(O): png.h pngconf.h pnglibconf.h
	$(CC) -c $(CFLAGS) $*.c $(ERRFILE)

pngtest.exe: pngtest.obj libpng.lib
	$(LD) $(LDFLAGS) pngtest.obj,,,libpng.lib ..\zlib\zlib.lib ;

test: pngtest.exe
	pngtest

# End of makefile for libpng

