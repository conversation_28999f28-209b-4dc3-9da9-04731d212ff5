

============================================================
                    Interactive Debugger
============================================================
Command (Exit)        : End Program
Command (Cont)        : Continue Execution
Command (Locals)      : Print local variables names
Command (LocalsData)  : Print local variables data
Command (Globals)     : Print global variables names
Command (CallStack)   : Print call stack
We can execute Ring code
============================================================

code:> x
name
data
data2

code:> 
Variable : x          Type : NUMBER          Value : 10
Variable : name       Type : STRING          Value : Mahmoud
Variable : data       Type : LIST            Value : one
two
three

Variable : data2      Type : LIST            Value : Egypt
KSA
USA


code:> Call Stack
==========
Called From: test1

code:> 