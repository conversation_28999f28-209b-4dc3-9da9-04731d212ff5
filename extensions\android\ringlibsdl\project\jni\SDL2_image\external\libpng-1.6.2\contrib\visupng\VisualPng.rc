//Microsoft Developer Studio generated resource script.
//
#include "resource.h"

#define APSTUDIO_READONLY_SYMBOLS
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 2 resource.
//
#include "afxres.h"

/////////////////////////////////////////////////////////////////////////////
#undef APSTUDIO_READONLY_SYMBOLS

/////////////////////////////////////////////////////////////////////////////
// English (U.S.) resources

#if !defined(AFX_RESOURCE_DLL) || defined(AFX_TARG_ENU)
#ifdef _WIN32
LANGUAGE LANG_ENGLISH, SUBLANG_ENGLISH_US
#pragma code_page(1252)
#endif //_WIN32

#ifdef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// TEXTINCLUDE
//

1 TEXTINCLUDE DISCARDABLE 
BEGIN
    "resource.h\0"
END

2 TEXTINCLUDE DISCARDABLE 
BEGIN
    "#include ""afxres.h""\r\n"
    "\0"
END

3 TEXTINCLUDE DISCARDABLE 
BEGIN
    "\r\n"
    "\0"
END

#endif    // APSTUDIO_INVOKED


/////////////////////////////////////////////////////////////////////////////
//
// Menu
//

VISUALPNG MENU DISCARDABLE 
BEGIN
    POPUP "&File"
    BEGIN
        MENUITEM "&Open Image...\tCtrl+O",      IDM_FILE_OPEN
        MENUITEM "Save &As...",                 IDM_FILE_SAVE
        MENUITEM SEPARATOR
        MENUITEM "&Next Image\tCtrl+N",         IDM_FILE_NEXT
        MENUITEM "Pre&vious Image\tCtrl+V",     IDM_FILE_PREVIOUS
        MENUITEM SEPARATOR
        MENUITEM "E&xit\tAlt+X",                IDM_FILE_EXIT
    END
    POPUP "&Options"
    BEGIN
        MENUITEM "&Stretch",                    IDM_OPTIONS_STRETCH, CHECKED
    END
    POPUP "&Help"
    BEGIN
        MENUITEM "&About",                      IDM_HELP_ABOUT
    END
END


/////////////////////////////////////////////////////////////////////////////
//
// Accelerator
//

VISUALPNG ACCELERATORS DISCARDABLE 
BEGIN
    "N",            IDM_FILE_NEXT,          VIRTKEY, CONTROL, NOINVERT
    "O",            IDM_FILE_OPEN,          VIRTKEY, CONTROL, NOINVERT
    "P",            IDM_FILE_PREVIOUS,      VIRTKEY, CONTROL, NOINVERT
    "V",            IDM_FILE_PREVIOUS,      VIRTKEY, CONTROL, NOINVERT
    "X",            IDM_FILE_EXIT,          VIRTKEY, ALT, NOINVERT
END


/////////////////////////////////////////////////////////////////////////////
//
// Icon
//

// Icon with lowest ID value placed first to ensure application icon
// remains consistent on all systems.
VISUALPNG               ICON    DISCARDABLE     "VisualPng.ico"

/////////////////////////////////////////////////////////////////////////////
//
// Dialog
//

ABOUTBOX DIALOG DISCARDABLE  0, 0, 186, 94
STYLE DS_MODALFRAME | WS_POPUP
FONT 8, "MS Sans Serif"
BEGIN
    DEFPUSHBUTTON   "OK",IDOK,68,67,50,14
    CTEXT           "VisualPng 1.0  -  June 2000",IDC_STATIC,49,14,88,8
    LTEXT           "a PNG image viewer",IDC_STATIC,60,30,66,8
    LTEXT           "(c) Willem van Schaik, 2000",IDC_STATIC,48,52,90,8
    LTEXT           "to demonstrate the use of libpng in Visual C",
                    IDC_STATIC,25,38,136,8
END


/////////////////////////////////////////////////////////////////////////////
//
// DESIGNINFO
//

#ifdef APSTUDIO_INVOKED
GUIDELINES DESIGNINFO DISCARDABLE 
BEGIN
    "ABOUTBOX", DIALOG
    BEGIN
        LEFTMARGIN, 7
        RIGHTMARGIN, 179
        TOPMARGIN, 7
        BOTTOMMARGIN, 87
    END
END
#endif    // APSTUDIO_INVOKED

#endif    // English (U.S.) resources
/////////////////////////////////////////////////////////////////////////////



#ifndef APSTUDIO_INVOKED
/////////////////////////////////////////////////////////////////////////////
//
// Generated from the TEXTINCLUDE 3 resource.
//


/////////////////////////////////////////////////////////////////////////////
#endif    // not APSTUDIO_INVOKED

