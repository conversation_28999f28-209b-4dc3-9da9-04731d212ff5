/*
  Simple DirectMedia Layer
  Copyright (C) 1997-2016 Sam <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.
*/

#ifndef VK_0
#define VK_0    '0'
#define VK_1    '1'
#define VK_2    '2'
#define VK_3    '3'
#define VK_4    '4'
#define VK_5    '5'
#define VK_6    '6'
#define VK_7    '7'
#define VK_8    '8'
#define VK_9    '9'
#define VK_A    'A'
#define VK_B    'B'
#define VK_C    'C'
#define VK_D    'D'
#define VK_E    'E'
#define VK_F    'F'
#define VK_G    'G'
#define VK_H    'H'
#define VK_I    'I'
#define VK_J    'J'
#define VK_K    'K'
#define VK_L    'L'
#define VK_M    'M'
#define VK_N    'N'
#define VK_O    'O'
#define VK_P    'P'
#define VK_Q    'Q'
#define VK_R    'R'
#define VK_S    'S'
#define VK_T    'T'
#define VK_U    'U'
#define VK_V    'V'
#define VK_W    'W'
#define VK_X    'X'
#define VK_Y    'Y'
#define VK_Z    'Z'
#endif /* VK_0 */

/* These keys haven't been defined, but were experimentally determined */
#define VK_SEMICOLON    0xBA
#define VK_EQUALS       0xBB
#define VK_COMMA        0xBC
#define VK_MINUS        0xBD
#define VK_PERIOD       0xBE
#define VK_SLASH        0xBF
#define VK_GRAVE        0xC0
#define VK_LBRACKET     0xDB
#define VK_BACKSLASH    0xDC
#define VK_RBRACKET     0xDD
#define VK_APOSTROPHE   0xDE
#define VK_BACKTICK     0xDF
#define VK_OEM_102      0xE2

/* vi: set ts=4 sw=4 expandtab: */
