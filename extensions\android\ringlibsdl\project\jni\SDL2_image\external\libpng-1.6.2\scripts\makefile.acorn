# Project:   libpng


# Toolflags:
CCflags = -c -depend !Depend -IC:,Zlib: -g -throwback  -DRISCOS  -fnah
C++flags = -c -depend !Depend -IC: -throwback
Linkflags = -aif -c++ -o $@
ObjAsmflags = -throwback -NoCache -depend !Depend
CMHGflags =
LibFileflags = -c -l -o $@
Squeezeflags = -o $@

# Final targets:
@.libpng-lib:   @.o.png @.o.pngerror @.o.pngrio @.o.pngwio @.o.pngmem \
	@.o.pngpread @.o.pngset @.o.pngget @.o.pngread @.o.pngrtran \
	@.o.pngrutil @.o.pngtrans @.o.pngwrite @.o.pngwtran @.o.pngwutil
	LibFile $(LibFileflags) @.o.png @.o.pngerror @.o.pngrio @.o.pngrtran \
	@.o.pngmem @.o.pngpread @.o.pngset @.o.pngget @.o.pngread @.o.pngwio \
	@.o.pngrutil @.o.pngtrans  @.o.pngwrite @.o.pngwtran @.o.pngwutil
@.mm-libpng-lib:   @.mm.png @.mm.pngerror @.mm.pngrio @.mm.pngwio @.mm.pngmem \
	@.mm.pngpread @.mm.pngset @.mm.pngget @.mm.pngread @.mm.pngrtran \
	@.mm.pngrutil @.mm.pngtrans @.mm.pngwrite @.mm.pngwtran @.mm.pngwutil
	LibFile $(LibFileflags) @.mm.png @.mm.pngerror @.mm.pngrio \
	@.mm.pngwio @.mm.pngmem @.mm.pngpread @.mm.pngset @.mm.pngget \
	@.mm.pngread @.mm.pngrtran @.mm.pngrutil @.mm.pngtrans @.mm.pngwrite \
	@.mm.pngwtran @.mm.pngwutil


# User-editable dependencies:
# (C) Copyright 1997 Tom Tanner
Test: @.pngtest
	<Prefix$Dir>.pngtest
	@remove <Prefix$Dir>.pngtest

#It would be nice if you could stop "make" listing from here on!
@.pngtest:   @.o.pngtest @.libpng-lib C:o.Stubs Zlib:zlib_lib
	Link $(Linkflags) @.o.pngtest @.libpng-lib C:o.Stubs Zlib:zlib_lib

.SUFFIXES: .o .mm .c

.c.mm:
	MemCheck.CC cc $(ccflags) -o $@ LibPng:$<
.c.o:
	cc $(ccflags) -o $@ $<

# See scripts.mak.libpngconf for how to generate this:
@.h.libpngconf: @.scripts.h.libpngconf
	copy @.scripts.h.libpngconf $@

# Static dependencies:
@.o.png @.o.pngerror @.o.pngrio @.o.pngwio @.o.pngmem \
@.o.pngpread @.o.pngset @.o.pngget @.o.pngread @.o.pngrtran \
@.o.pngrutil @.o.pngtrans @.o.pngwrite @.o.pngwtran @.o.pngwutil \
@.o.pngtest: @.h.libpngconf


# Dynamic dependencies:
