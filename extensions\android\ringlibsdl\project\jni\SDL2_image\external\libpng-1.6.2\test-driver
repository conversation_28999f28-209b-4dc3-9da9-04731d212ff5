#! /bin/sh
# test-driver - basic testsuite driver script.

scriptversion=2012-06-27.10; # UTC

# Copyright (C) 2011-2012 Free Software Foundation, Inc.
#
# This program is free software; you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation; either version 2, or (at your option)
# any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

# As a special exception to the GNU General Public License, if you
# distribute this file as part of a program that contains a
# configuration script generated by Autoconf, you may include it under
# the same distribution terms that you use for the rest of that program.

# This file is maintained in Automake, please report
# <AUTHOR> <EMAIL> or send patches to
# <<EMAIL>>.

# Make unconditional expansion of undefined variables an error.  This
# helps a lot in preventing typo-related bugs.
set -u

usage_error ()
{
  echo "$0: $*" >&2
  print_usage >&2
  exit 2
}

print_usage ()
{
  cat <<END
Usage:
  test-driver --test-name=NAME --log-file=PATH --trs-file=PATH
              [--expect-failure={yes|no}] [--color-tests={yes|no}]
              [--enable-hard-errors={yes|no}] [--] TEST-SCRIPT
The '--test-name', '--log-file' and '--trs-file' options are mandatory.
END
}

# TODO: better error handling in option parsing (in particular, ensure
# TODO: $log_file, $trs_file and $test_name are defined).
test_name= # Used for reporting.
log_file=  # Where to save the output of the test script.
trs_file=  # Where to save the metadata of the test run.
expect_failure=no
color_tests=no
enable_hard_errors=yes
while test $# -gt 0; do
  case $1 in
  --help) print_usage; exit $?;;
  --version) echo "test-driver $scriptversion"; exit $?;;
  --test-name) test_name=$2; shift;;
  --log-file) log_file=$2; shift;;
  --trs-file) trs_file=$2; shift;;
  --color-tests) color_tests=$2; shift;;
  --expect-failure) expect_failure=$2; shift;;
  --enable-hard-errors) enable_hard_errors=$2; shift;;
  --) shift; break;;
  -*) usage_error "invalid option: '$1'";;
  esac
  shift
done

if test $color_tests = yes; then
  # Keep this in sync with 'lib/am/check.am:$(am__tty_colors)'.
  red='[0;31m' # Red.
  grn='[0;32m' # Green.
  lgn='[1;32m' # Light green.
  blu='[1;34m' # Blue.
  mgn='[0;35m' # Magenta.
  std='[m'     # No color.
else
  red= grn= lgn= blu= mgn= std=
fi

do_exit='rm -f $log_file $trs_file; (exit $st); exit $st'
trap "st=129; $do_exit" 1
trap "st=130; $do_exit" 2
trap "st=141; $do_exit" 13
trap "st=143; $do_exit" 15

# Test script is run here.
"$@" >$log_file 2>&1
estatus=$?
if test $enable_hard_errors = no && test $estatus -eq 99; then
  estatus=1
fi

case $estatus:$expect_failure in
  0:yes) col=$red res=XPASS recheck=yes gcopy=yes;;
  0:*)   col=$grn res=PASS  recheck=no  gcopy=no;;
  77:*)  col=$blu res=SKIP  recheck=no  gcopy=yes;;
  99:*)  col=$mgn res=ERROR recheck=yes gcopy=yes;;
  *:yes) col=$lgn res=XFAIL recheck=no  gcopy=yes;;
  *:*)   col=$red res=FAIL  recheck=yes gcopy=yes;;
esac

# Report outcome to console.
echo "${col}${res}${std}: $test_name"

# Register the test result, and other relevant metadata.
echo ":test-result: $res" > $trs_file
echo ":global-test-result: $res" >> $trs_file
echo ":recheck: $recheck" >> $trs_file
echo ":copy-in-global-log: $gcopy" >> $trs_file

# Local Variables:
# mode: shell-script
# sh-indentation: 2
# eval: (add-hook 'write-file-hooks 'time-stamp)
# time-stamp-start: "scriptversion="
# time-stamp-format: "%:y-%02m-%02d.%02H"
# time-stamp-time-zone: "UTC"
# time-stamp-end: "; # UTC"
# End:
