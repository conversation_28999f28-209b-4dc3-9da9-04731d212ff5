/*
  Simple DirectMedia Layer
  Copyright (C) 1997-2016 <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.
*/

#ifndef _SDL_DirectFB_mouse_h
#define _SDL_DirectFB_mouse_h

#include <directfb.h>

#include "../SDL_sysvideo.h"

typedef struct _DFB_CursorData DFB_CursorData;
struct _DFB_CursorData
{
    IDirectFBSurface *surf;
    int             hotx;
    int             hoty;
};

#define SDL_DFB_CURSORDATA(curs)  DFB_CursorData *curdata = (DFB_CursorData *) ((curs) ? (curs)->driverdata : NULL)

extern void DirectFB_InitMouse(_THIS);
extern void DirectFB_QuitMouse(_THIS);

#endif /* _SDL_DirectFB_mouse_h */

/* vi: set ts=4 sw=4 expandtab: */
