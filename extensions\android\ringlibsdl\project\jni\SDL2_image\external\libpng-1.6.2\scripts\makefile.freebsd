# makefile for libpng under FreeBSD
# Copyright (C) 2002, 2007, 2009 <PERSON> and <PERSON><PERSON>
#
# This code is released under the libpng license.
# For conditions of distribution and use, see the disclaimer
# and license in png.h

PREFIX?=        /usr/local
SHLIB_VER?=     16

LIB=		png
SHLIB_MAJOR=	${SHLIB_VER}
SHLIB_MINOR=	0
NO_PROFILE=	YES
NO_OBJ=          YES

# where make install puts libpng.a and png.h
DESTDIR=	${PREFIX}
LIBDIR=		/lib
INCS=		png.h pngconf.h pnglibconf.h pngpriv.h pngstruct.h             pnginfo.h pngdebug.h
INCSDIR=	/include/libpng
INCDIR=		${INCSDIR}		# for 4.x bsd.lib.mk
MAN=		libpng.3 libpngpf.3 png.5
MANDIR=		/man/man
SYMLINKS=       libpng/png.h ${INCSDIR}/../png.h \
		libpng/pngconf.h ${INCSDIR}/../pngconf.h \
		libpng/pnglibconf.h ${INCSDIR}/../pnglibconf.h

# where make install finds libz.a and zlib.h
ZLIBLIB=	/usr/lib
ZLIBINC=	/usr/include

LDADD+=        -lm -lz
#LDADD+=       -lm -lz -lssp_nonshared   # for OSVERSION < 800000 ?

DPADD+=         ${LIBM} ${LIBZ}

CFLAGS+= -I. -I${ZLIBINC}

SRCS=	png.c pngset.c pngget.c pngrutil.c pngtrans.c pngwutil.c \
	pngread.c pngrio.c pngwio.c pngwrite.c pngrtran.c \
	pngwtran.c pngmem.c pngerror.c pngpread.c

pngtest: pngtest.o libpng.a
	${CC} ${CFLAGS} -L. -static -o pngtest pngtest.o -L${ZLIBLIB} \
	-lpng ${LDADD}

CLEANFILES= pngtest pngtest.o pngout.png

test: pngtest
	./pngtest

# see scripts/pnglibconf.mak for more options
pnglibconf.h: scripts/pnglibconf.h.prebuilt
	cp scripts/pnglibconf.h.prebuilt $@

DOCS = ANNOUNCE CHANGES INSTALL KNOWNBUG LICENSE README TODO Y2KINFO
writelock:
	chmod a-w *.[ch35] $(DOCS) scripts/*

.include <bsd.lib.mk>
