test1

Line 13 Error (R3) : Calling Function without definition: test2 
In function test1() in file tracelib/test4.ring

Called from line 6 in file tracelib/test4.ring 

============================================================
                    Interactive Debugger
============================================================
Command (Exit)        : End Program
Command (Cont)        : Continue Execution
Command (Locals)      : Print local variables names
Command (LocalsData)  : Print local variables data
Command (Globals)     : Print global variables names
Command (CallStack)   : Print call stack
We can execute Ring code
============================================================

code:> x
t

code:> 
Variable : x      Type : NUMBER          Value : 10
Variable : t      Type : NUMBER          Value : 12

code:> After Error!
t = 12
x = 10
good bye!
