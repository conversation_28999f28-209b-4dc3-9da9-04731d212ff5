LOCAL_PATH:= $(call my-dir)
include $(CLEAR_VARS)

LOCAL_ARM_MODE := arm

LOCAL_SRC_FILES := \
    jaricom.c jcapimin.c jcapistd.c jcarith.c jccoefct.c jccolor.c \
    jcdctmgr.c jchuff.c jcinit.c jcmainct.c jcmarker.c jcmaster.c \
    jcomapi.c jcparam.c jcprepct.c jcsample.c jctrans.c jdapimin.c \
    jdapistd.c jdarith.c jdatadst.c jdatasrc.c jdcoefct.c jdcolor.c \
    jddctmgr.c jdhuff.c jdinput.c jdmainct.c jdmarker.c jdmaster.c \
    jdmerge.c jdpostct.c jdsample.c jdtrans.c jerror.c jfdctflt.c \
    jfdctfst.c jfdctint.c jidctflt.c jidctfst.c jidctint.c jquant1.c \
    jquant2.c jutils.c jmemmgr.c \
	jmem-android.c

# the assembler is only for the ARM version, don't break the Linux sim
ifneq ($(TARGET_ARCH),arm)
ANDROID_JPEG_NO_ASSEMBLER := true
endif

# temp fix until we understand why this broke cnn.com
#ANDROID_JPEG_NO_ASSEMBLER := true

ifeq ($(strip $(ANDROID_JPEG_NO_ASSEMBLER)),true)
LOCAL_SRC_FILES += jidctint.c jidctfst.c
else
LOCAL_SRC_FILES += jidctint.c jidctfst.S
endif

LOCAL_CFLAGS += -DAVOID_TABLES 
LOCAL_CFLAGS += -O3 -fstrict-aliasing -fprefetch-loop-arrays
#LOCAL_CFLAGS += -march=armv6j

LOCAL_MODULE:= jpeg

include $(BUILD_STATIC_LIBRARY)
