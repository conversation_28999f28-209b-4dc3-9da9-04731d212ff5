#!/bin/sh
# -- grayscale
./pnm2png basn0g01.pgm basn0g01.png
./pnm2png basn0g02.pgm basn0g02.png
./pnm2png basn0g04.pgm basn0g04.png
./pnm2png basn0g08.pgm basn0g08.png
./pnm2png basn0g16.pgm basn0g16.png
# -- full-color
./pnm2png basn2c08.ppm basn2c08.png
./pnm2png basn2c16.ppm basn2c16.png
# -- palletted
./pnm2png basn3p01.ppm basn3p01.png
./pnm2png basn3p02.ppm basn3p02.png
./pnm2png basn3p04.ppm basn3p04.png
./pnm2png basn3p08.ppm basn3p08.png
# -- gray with alpha-channel
./pnm2png -alpha basn6a08.pgm basn4a08.pgm basn4a08.png
./pnm2png -alpha basn6a16.pgm basn4a16.pgm basn4a16.png
# -- color with alpha-channel
./pnm2png -alpha basn6a08.pgm basn6a08.ppm basn6a08.png
./pnm2png -alpha basn6a16.pgm basn6a16.ppm basn6a16.png
# -- grayscale
./pnm2png rawn0g01.pgm rawn0g01.png
./pnm2png rawn0g02.pgm rawn0g02.png
./pnm2png rawn0g04.pgm rawn0g04.png
./pnm2png rawn0g08.pgm rawn0g08.png
./pnm2png rawn0g16.pgm rawn0g16.png
# -- full-color
./pnm2png rawn2c08.ppm rawn2c08.png
./pnm2png rawn2c16.ppm rawn2c16.png
# -- palletted
./pnm2png rawn3p01.ppm rawn3p01.png
./pnm2png rawn3p02.ppm rawn3p02.png
./pnm2png rawn3p04.ppm rawn3p04.png
./pnm2png rawn3p08.ppm rawn3p08.png
# -- gray with alpha-channel
./pnm2png -alpha rawn6a08.pgm rawn4a08.pgm rawn4a08.png
./pnm2png -alpha rawn6a16.pgm rawn4a16.pgm rawn4a16.png
# -- color with alpha-channel
./pnm2png -alpha rawn6a08.pgm rawn6a08.ppm rawn6a08.png
./pnm2png -alpha rawn6a16.pgm rawn6a16.ppm rawn6a16.png

