# Makefile for PngMinus (rpng2)
# Linux / Unix

#CC=cc
CC=gcc
LD=$(CC)

# If awk fails try
# make AWK=nawk

# If cpp fails try
# make CPP=/lib/cpp

RM=rm -f
COPY=cp

#XINC = -I/usr/include			# old-style, stock X distributions
#XLIB = -L/usr/lib/X11 -lX11		#  (including SGI IRIX)

#XINC = -I/usr/openwin/include		# Sun workstations (OpenWindows)
#XLIB = -L/usr/openwin/lib -lX11

XINC = -I/usr/X11R6/include		# new X distributions (X.org, etc.)
XLIB = -L/usr/X11R6/lib -lX11
#XLIB = -L/usr/X11R6/lib64 -lX11	# e.g., Red Hat on AMD64

#XINC = -I/usr/local/include   		# FreeBSD
#XLIB = -L/usr/local/lib -lX11

#LIBS = $(XLIB)
LIBS = $(XLIB) -lm                      #platforms that need libm

CFLAGS=-DPNG_USER_CONFIG -DNO_GZCOMPRESS -DZ_SOLO -DNO_GZIP -I. $(XINC) -O1

C=.c
O=.o
L=.a
E=

# Where to find the source code:
PNGSRC =../../..
ZLIBSRC=$(PNGSRC)/../zlib
PROGSRC=$(PNGSRC)/contrib/gregbook

# Zlib (minimal inflate requirements - crc32 is used by libpng)
# zutil can be eliminated if you provide your own zcalloc and zcfree
ZSRCS  = adler32$(C) crc32$(C) \
	 inffast$(C) inflate$(C) inftrees$(C) \
	 zutil$(C)

# Standard headers
ZH     = zlib.h crc32.h inffast.h inffixed.h \
	 inflate.h inftrees.h zutil.h

# Machine generated headers
ZCONF  = zconf.h

# Headers callers use
ZINC   = zlib.h $(ZCONF)

# Headers the Zlib source uses
ZHDRS  = $(ZH) $(ZCONF)

ZOBJS  = adler32$(O) crc32$(O) \
	 inffast$(O) inflate$(O) inftrees$(O) \
	 zutil$(O)

# libpng
PNGSRCS=png$(C) pngerror$(C) pngget$(C) pngmem$(C) \
	pngpread$(C) pngread$(C) pngrio$(C) pngrtran$(C) pngrutil$(C) \
	pngset$(C) pngtrans$(C)

# Standard headers
PNGH   =png.h pngconf.h pngdebug.h pnginfo.h pngpriv.h pngstruct.h

# Machine generated headers
PNGCONF=pnglibconf.h

# Headers callers use
PNGINC= png.h pngconf.h pngusr.h $(PNGCONF)

# Headers the PNG library uses
PNGHDRS=$(PNGH) $(PNGCONF) pngusr.h

PNGOBJS=png$(O) pngerror$(O) pngget$(O) pngmem$(O) \
	pngpread$(O) pngread$(O) pngrio$(O) pngrtran$(O) pngrutil$(O) \
	pngset$(O) pngtrans$(O)

PROGSRCS= rpng2-x$(C) readpng2$(C)
PROGHDRS= readpng2.h
PROGDOCS= COPYING LICENSE
PROGOBJS= rpng2-x$(O) readpng2$(O)

OBJS    = $(PROGOBJS) $(PNGOBJS) $(ZOBJS)

# implicit make rules -------------------------------------------------------

.c$(O):
	$(CC) -c $(CFLAGS) $<

# dependencies

all: $(PROGDOCS) rpng2-x$(E)

rpng2-x$(E): $(OBJS)
	$(LD) -o rpng2-x$(E) $(OBJS) $(LIBS)

# The DFA_XTRA setting turns all libpng options off then
# turns on those required for this minimal build.
# The CPP_FLAGS setting causes pngusr.h to be included in
# both the build of pnglibconf.h and, subsequently, when
# building libpng itself.
$(PNGCONF): $(PNGSRC)/scripts/pnglibconf.mak\
	$(PNGSRC)/scripts/pnglibconf.dfa \
	$(PNGSRC)/scripts/options.awk pngusr.h pngusr.dfa
	$(RM) pnglibconf.h pnglibconf.dfn
	$(MAKE) $(MAKEFLAGS) -f $(PNGSRC)/scripts/pnglibconf.mak\
	    srcdir=$(PNGSRC) CPPFLAGS="-DPNG_USER_CONFIG"\
	    DFA_XTRA="pngusr.dfa" $@

clean:
	$(MAKE) $(MAKEFLAGS) -f $(PNGSRC)/scripts/pnglibconf.mak\
	    srcdir=$(PNGSRC) clean
	$(RM) rpng2-x$(O)
	$(RM) rpng2-x$(E)
	$(RM) $(OBJS)

# distclean also removes the copied source and headers
distclean: clean
	$(RM) -r scripts # historical reasons
	$(RM) $(PNGSRCS) $(PNGH)
	$(RM) $(ZSRCS) $(ZH) $(ZCONF)
	$(RM) $(PROGSRCS) $(PROGHDRS) $(PROGDOCS)

# Header file dependencies:
$(PROGOBJS): $(PROGHDRS) $(PNGINC) $(ZINC)
$(PNGOBJS): $(PNGHDRS) $(ZINC)
$(ZOBJS): $(ZHDRS)

# Gather the source code from the respective directories
$(PNGSRCS) $(PNGH): $(PNGSRC)/$@
	$(RM) $@
	$(COPY) $(PNGSRC)/$@ $@

# No dependency on the ZLIBSRC target so that it only needs
# to be specified once.
$(ZSRCS) $(ZH):
	$(RM) $@
	$(COPY) $(ZLIBSRC)/$@ $@

# The unconfigured zconf.h varies in name according to the
# zlib release
$(ZCONF):
	$(RM) $@
	@for f in zconf.h.in zconf.in.h zconf.h; do\
	    test -r $(ZLIBSRC)/$$f &&\
	    echo $(COPY) $(ZLIBSRC)/$$f $@ &&\
	    $(COPY) $(ZLIBSRC)/$$f $@ && exit 0;\
	done; echo copy: $(ZLIBSRC)/zconf.h not found; exit 1

$(PROGSRCS) $(PROGHDRS) $(PROGDOCS): $(PROGSRC)/$@
	$(RM) $@
	$(COPY) $(PROGSRC)/$@ $@

# End of makefile for rpng2-x
