/*
  Simple DirectMedia Layer
  Copyright (C) 1997-2016 <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.
*/

#ifndef SDL_BOPENGL_H
#define SDL_BOPENGL_H

#ifdef __cplusplus
extern "C" {
#endif

#include "../SDL_sysvideo.h"


extern int BE_GL_LoadLibrary(_THIS, const char *path);                  /* FIXME */
extern void *BE_GL_GetProcAddress(_THIS, const char *proc);             /* FIXME */
extern void BE_GL_UnloadLibrary(_THIS);                                 /* TODO */
extern int BE_GL_MakeCurrent(_THIS, SDL_Window * window,
                              SDL_GLContext context);
extern int BE_GL_SetSwapInterval(_THIS, int interval);                  /* TODO */
extern int BE_GL_GetSwapInterval(_THIS);                                /* TODO */
extern void BE_GL_SwapWindow(_THIS, SDL_Window * window);
extern SDL_GLContext BE_GL_CreateContext(_THIS, SDL_Window * window);
extern void BE_GL_DeleteContext(_THIS, SDL_GLContext context);

extern void BE_GL_RebootContexts(_THIS);

#ifdef __cplusplus
}
#endif

#endif
