/* DO NOT EDIT!  This file is generated by sdlgenblit.pl */
/*
  Simple DirectMedia Layer
  Copyright (C) 1997-2016 <PERSON> <<EMAIL>>

  This software is provided 'as-is', without any express or implied
  warranty.  In no event will the authors be held liable for any damages
  arising from the use of this software.

  Permission is granted to anyone to use this software for any purpose,
  including commercial applications, and to alter it and redistribute it
  freely, subject to the following restrictions:

  1. The origin of this software must not be misrepresented; you must not
     claim that you wrote the original software. If you use this software
     in a product, an acknowledgment in the product documentation would be
     appreciated but is not required.
  2. Altered source versions must be plainly marked as such, and must not be
     misrepresented as being the original software.
  3. This notice may not be removed or altered from any source distribution.
*/
#include "../SDL_internal.h"

/* *INDENT-OFF* */

#include "SDL_video.h"
#include "SDL_blit.h"
#include "SDL_blit_auto.h"

static void SDL_Blit_RGB888_RGB888_Scale(SDL_BlitInfo *info)
{
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            *dst = *src;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_RGB888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_RGB888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_RGB888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_RGB888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_RGB888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_RGB888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_BGR888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_BGR888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_BGR888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_BGR888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_BGR888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_BGR888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_BGR888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_ARGB8888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel; A = 0xFF;
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_ARGB8888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_ARGB8888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_ARGB8888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel; A = 0xFF;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_ARGB8888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel; A = 0xFF;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_ARGB8888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGB888_ARGB8888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_RGB888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_RGB888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_RGB888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_RGB888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_RGB888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_RGB888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_RGB888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_BGR888_Scale(SDL_BlitInfo *info)
{
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            *dst = *src;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_BGR888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_BGR888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_BGR888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_BGR888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_BGR888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_BGR888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_ARGB8888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel; A = 0xFF;
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_ARGB8888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_ARGB8888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_ARGB8888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel; A = 0xFF;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_ARGB8888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel; A = 0xFF;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_ARGB8888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGR888_ARGB8888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = 0xFF;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_RGB888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_RGB888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_RGB888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_RGB888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_RGB888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_RGB888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_RGB888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_BGR888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_BGR888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_BGR888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_BGR888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_BGR888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_BGR888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_BGR888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_ARGB8888_Scale(SDL_BlitInfo *info)
{
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            *dst = *src;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_ARGB8888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_ARGB8888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_ARGB8888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel; A = (Uint8)(pixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_ARGB8888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); B = (Uint8)pixel; A = (Uint8)(pixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_ARGB8888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ARGB8888_ARGB8888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcB = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_RGB888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8);
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_RGB888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_RGB888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_RGB888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_RGB888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_RGB888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_RGB888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_BGR888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8);
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_BGR888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_BGR888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_BGR888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_BGR888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_BGR888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_BGR888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_ARGB8888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8); A = (Uint8)pixel;
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_ARGB8888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_ARGB8888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_ARGB8888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8); A = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_ARGB8888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            R = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); B = (Uint8)(pixel >> 8); A = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_ARGB8888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_RGBA8888_ARGB8888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcR = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcB = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_RGB888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_RGB888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_RGB888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_RGB888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_RGB888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_RGB888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_RGB888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_BGR888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_BGR888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_BGR888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_BGR888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_BGR888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_BGR888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_BGR888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_ARGB8888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel; A = (Uint8)(pixel >> 24);
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_ARGB8888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_ARGB8888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_ARGB8888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel; A = (Uint8)(pixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_ARGB8888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 16); G = (Uint8)(pixel >> 8); R = (Uint8)pixel; A = (Uint8)(pixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_ARGB8888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_ABGR8888_ARGB8888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 16); srcG = (Uint8)(srcpixel >> 8); srcR = (Uint8)srcpixel; srcA = (Uint8)(srcpixel >> 24);
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_RGB888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8);
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_RGB888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_RGB888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_RGB888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_RGB888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_RGB888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_RGB888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_BGR888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8);
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_BGR888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_BGR888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_BGR888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_BGR888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    Uint32 pixel;
    Uint32 R, G, B;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            pixel = ((Uint32)B << 16) | ((Uint32)G << 8) | R;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_BGR888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_BGR888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstB = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstR = (Uint8)dstpixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstB << 16) | ((Uint32)dstG << 8) | dstR;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_ARGB8888_Scale(SDL_BlitInfo *info)
{
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8); A = (Uint8)pixel;
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_ARGB8888_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_ARGB8888_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_ARGB8888_Modulate(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8); A = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_ARGB8888_Modulate_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 pixel;
    Uint32 R, G, B, A;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            pixel = *src;
            B = (Uint8)(pixel >> 24); G = (Uint8)(pixel >> 16); R = (Uint8)(pixel >> 8); A = (Uint8)pixel;
            if (flags & SDL_COPY_MODULATE_COLOR) {
                R = (R * modulateR) / 255;
                G = (G * modulateG) / 255;
                B = (B * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                A = (A * modulateA) / 255;
            }
            pixel = ((Uint32)A << 24) | ((Uint32)R << 16) | ((Uint32)G << 8) | B;
            *dst = pixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_ARGB8888_Modulate_Blend(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;

    while (info->dst_h--) {
        Uint32 *src = (Uint32 *)info->src;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        while (n--) {
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            ++src;
            ++dst;
        }
        info->src += info->src_pitch;
        info->dst += info->dst_pitch;
    }
}

static void SDL_Blit_BGRA8888_ARGB8888_Modulate_Blend_Scale(SDL_BlitInfo *info)
{
    const int flags = info->flags;
    const Uint32 modulateR = info->r;
    const Uint32 modulateG = info->g;
    const Uint32 modulateB = info->b;
    const Uint32 modulateA = info->a;
    Uint32 srcpixel;
    Uint32 srcR, srcG, srcB, srcA;
    Uint32 dstpixel;
    Uint32 dstR, dstG, dstB, dstA;
    int srcy, srcx;
    int posy, posx;
    int incy, incx;

    srcy = 0;
    posy = 0;
    incy = (info->src_h << 16) / info->dst_h;
    incx = (info->src_w << 16) / info->dst_w;

    while (info->dst_h--) {
        Uint32 *src = 0;
        Uint32 *dst = (Uint32 *)info->dst;
        int n = info->dst_w;
        srcx = -1;
        posx = 0x10000L;
        while (posy >= 0x10000L) {
            ++srcy;
            posy -= 0x10000L;
        }
        while (n--) {
            if (posx >= 0x10000L) {
                while (posx >= 0x10000L) {
                    ++srcx;
                    posx -= 0x10000L;
                }
                src = (Uint32 *)(info->src + (srcy * info->src_pitch) + (srcx * 4));
            }
            srcpixel = *src;
            srcB = (Uint8)(srcpixel >> 24); srcG = (Uint8)(srcpixel >> 16); srcR = (Uint8)(srcpixel >> 8); srcA = (Uint8)srcpixel;
            dstpixel = *dst;
            dstR = (Uint8)(dstpixel >> 16); dstG = (Uint8)(dstpixel >> 8); dstB = (Uint8)dstpixel; dstA = (Uint8)(dstpixel >> 24);
            if (flags & SDL_COPY_MODULATE_COLOR) {
                srcR = (srcR * modulateR) / 255;
                srcG = (srcG * modulateG) / 255;
                srcB = (srcB * modulateB) / 255;
            }
            if (flags & SDL_COPY_MODULATE_ALPHA) {
                srcA = (srcA * modulateA) / 255;
            }
            if (flags & (SDL_COPY_BLEND|SDL_COPY_ADD)) {
                /* This goes away if we ever use premultiplied alpha */
                if (srcA < 255) {
                    srcR = (srcR * srcA) / 255;
                    srcG = (srcG * srcA) / 255;
                    srcB = (srcB * srcA) / 255;
                }
            }
            switch (flags & (SDL_COPY_BLEND|SDL_COPY_ADD|SDL_COPY_MOD)) {
            case SDL_COPY_BLEND:
                dstR = srcR + ((255 - srcA) * dstR) / 255;
                dstG = srcG + ((255 - srcA) * dstG) / 255;
                dstB = srcB + ((255 - srcA) * dstB) / 255;
                dstA = srcA + ((255 - srcA) * dstA) / 255;
                break;
            case SDL_COPY_ADD:
                dstR = srcR + dstR; if (dstR > 255) dstR = 255;
                dstG = srcG + dstG; if (dstG > 255) dstG = 255;
                dstB = srcB + dstB; if (dstB > 255) dstB = 255;
                break;
            case SDL_COPY_MOD:
                dstR = (srcR * dstR) / 255;
                dstG = (srcG * dstG) / 255;
                dstB = (srcB * dstB) / 255;
                break;
            }
            dstpixel = ((Uint32)dstA << 24) | ((Uint32)dstR << 16) | ((Uint32)dstG << 8) | dstB;
            *dst = dstpixel;
            posx += incx;
            ++dst;
        }
        posy += incy;
        info->dst += info->dst_pitch;
    }
}

SDL_BlitFuncEntry SDL_GeneratedBlitFuncTable[] = {
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_RGB888_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGB888_RGB888_Blend },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_RGB888_Blend_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_RGB888_RGB888_Modulate },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_RGB888_Modulate_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGB888_RGB888_Modulate_Blend },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_RGB888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_BGR888_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGB888_BGR888_Blend },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_BGR888_Blend_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_RGB888_BGR888_Modulate },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_BGR888_Modulate_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGB888_BGR888_Modulate_Blend },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_BGR888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_ARGB8888_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGB888_ARGB8888_Blend },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_ARGB8888_Blend_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_RGB888_ARGB8888_Modulate },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_ARGB8888_Modulate_Scale },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGB888_ARGB8888_Modulate_Blend },
    { SDL_PIXELFORMAT_RGB888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGB888_ARGB8888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_RGB888_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGR888_RGB888_Blend },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_RGB888_Blend_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_BGR888_RGB888_Modulate },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_RGB888_Modulate_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGR888_RGB888_Modulate_Blend },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_RGB888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_BGR888_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGR888_BGR888_Blend },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_BGR888_Blend_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_BGR888_BGR888_Modulate },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_BGR888_Modulate_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGR888_BGR888_Modulate_Blend },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_BGR888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_ARGB8888_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGR888_ARGB8888_Blend },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_ARGB8888_Blend_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_BGR888_ARGB8888_Modulate },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_ARGB8888_Modulate_Scale },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGR888_ARGB8888_Modulate_Blend },
    { SDL_PIXELFORMAT_BGR888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGR888_ARGB8888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_RGB888_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ARGB8888_RGB888_Blend },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_RGB888_Blend_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_ARGB8888_RGB888_Modulate },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_RGB888_Modulate_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ARGB8888_RGB888_Modulate_Blend },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_RGB888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_BGR888_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ARGB8888_BGR888_Blend },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_BGR888_Blend_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_ARGB8888_BGR888_Modulate },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_BGR888_Modulate_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ARGB8888_BGR888_Modulate_Blend },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_BGR888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_ARGB8888_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ARGB8888_ARGB8888_Blend },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_ARGB8888_Blend_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_ARGB8888_ARGB8888_Modulate },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_ARGB8888_Modulate_Scale },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ARGB8888_ARGB8888_Modulate_Blend },
    { SDL_PIXELFORMAT_ARGB8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ARGB8888_ARGB8888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_RGB888_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGBA8888_RGB888_Blend },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_RGB888_Blend_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_RGBA8888_RGB888_Modulate },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_RGB888_Modulate_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGBA8888_RGB888_Modulate_Blend },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_RGB888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_BGR888_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGBA8888_BGR888_Blend },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_BGR888_Blend_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_RGBA8888_BGR888_Modulate },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_BGR888_Modulate_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGBA8888_BGR888_Modulate_Blend },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_BGR888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_ARGB8888_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGBA8888_ARGB8888_Blend },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_ARGB8888_Blend_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_RGBA8888_ARGB8888_Modulate },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_ARGB8888_Modulate_Scale },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_RGBA8888_ARGB8888_Modulate_Blend },
    { SDL_PIXELFORMAT_RGBA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_RGBA8888_ARGB8888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_RGB888_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ABGR8888_RGB888_Blend },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_RGB888_Blend_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_ABGR8888_RGB888_Modulate },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_RGB888_Modulate_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ABGR8888_RGB888_Modulate_Blend },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_RGB888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_BGR888_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ABGR8888_BGR888_Blend },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_BGR888_Blend_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_ABGR8888_BGR888_Modulate },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_BGR888_Modulate_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ABGR8888_BGR888_Modulate_Blend },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_BGR888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_ARGB8888_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ABGR8888_ARGB8888_Blend },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_ARGB8888_Blend_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_ABGR8888_ARGB8888_Modulate },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_ARGB8888_Modulate_Scale },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_ABGR8888_ARGB8888_Modulate_Blend },
    { SDL_PIXELFORMAT_ABGR8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_ABGR8888_ARGB8888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_RGB888_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGRA8888_RGB888_Blend },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_RGB888_Blend_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_BGRA8888_RGB888_Modulate },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_RGB888_Modulate_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGRA8888_RGB888_Modulate_Blend },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_RGB888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_RGB888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_BGR888_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGRA8888_BGR888_Blend },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_BGR888_Blend_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_BGRA8888_BGR888_Modulate },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_BGR888_Modulate_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGRA8888_BGR888_Modulate_Blend },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_BGR888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_BGR888_Modulate_Blend_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_ARGB8888_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGRA8888_ARGB8888_Blend },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_ARGB8888_Blend_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA), SDL_CPU_ANY, SDL_Blit_BGRA8888_ARGB8888_Modulate },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_ARGB8888_Modulate_Scale },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD), SDL_CPU_ANY, SDL_Blit_BGRA8888_ARGB8888_Modulate_Blend },
    { SDL_PIXELFORMAT_BGRA8888, SDL_PIXELFORMAT_ARGB8888, (SDL_COPY_MODULATE_COLOR | SDL_COPY_MODULATE_ALPHA | SDL_COPY_BLEND | SDL_COPY_ADD | SDL_COPY_MOD | SDL_COPY_NEAREST), SDL_CPU_ANY, SDL_Blit_BGRA8888_ARGB8888_Modulate_Blend_Scale },
    { 0, 0, 0, 0, NULL }
};

/* *INDENT-ON* */

/* vi: set ts=4 sw=4 expandtab: */
